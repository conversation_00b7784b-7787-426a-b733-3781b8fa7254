<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Tolk</string>
		<key>CFBundleDocumentTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeName</key>
				<string>All Files</string>
				<key>CFBundleTypeRole</key>
				<string>Viewer</string>
				<key>LSItemContentTypes</key>
				<array>
					<string>public.item</string>
					<string>public.content</string>
					<string>public.data</string>
				</array>
			</dict>
		</array>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Tolk</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.393289530782-i5fd6ec8fh5sqgfr1ag1cjodc8rudgkt</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>maps</string>
			<string>comgooglemaps</string>
			<string>http</string>
			<string>https</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>LSSupportsOpeningDocumentsInPlace</key>
		<true/>
		<key>FlutterDeepLinkingEnabled</key>
		<false/>
		<key>NSCameraUsageDescription</key>
		<string>Take photos and make video calls to share in your conversations.</string>
		<key>NSContactsUsageDescription</key>
		<string>Access your contacts to find friends and start conversations.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Share your current location with friends in your conversations.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Share your current location with friends in your conversations.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Record voice messages and make audio/video calls in your conversations.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Select photos and videos to share in your conversations.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIFileSharingEnabled</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
	</dict>
</plist>