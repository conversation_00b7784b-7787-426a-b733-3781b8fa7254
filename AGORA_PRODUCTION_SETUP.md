# Agora.io Production Setup Guide

This guide will help you set up Agora.io for production use with proper security, scalability, and best practices.

## 1. Agora Console Setup for Production

### Step 1: Create Production Project
1. Go to [Agora Console](https://console.agora.io/)
2. Click "Create Project"
3. **Project Name**: Use your app name (e.g., "Tolk Production")
4. **Authentication**: Choose "Secured mode: APP ID + Token" (REQUIRED for production)
5. Click "Submit"
6. Save your **App ID** and **App Certificate**

### Step 2: Enable Required Features
1. In your project dashboard, go to "Feature Config"
2. Enable the following:
   - **Real-time Messaging (RTM)**: For call signaling
   - **Cloud Recording**: For call recording (optional)
   - **Co-host Token**: For advanced call management

## 2. Token Server Implementation

For production, you MUST implement token authentication. Here are the options:

### Option A: Cloud Function Token Server (Recommended)

Create a Firebase Cloud Function to generate Agora tokens:

```javascript
// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const { RtcTokenBuilder, RtcRole } = require('agora-access-token');

// Initialize Firebase Admin
admin.initializeApp();

// Your Agora credentials
const APP_ID = 'your_agora_app_id';
const APP_CERTIFICATE = 'your_agora_app_certificate';

exports.generateAgoraToken = functions.https.onCall(async (data, context) => {
  // Verify user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { channelName, uid, role = 'publisher' } = data;

  if (!channelName || !uid) {
    throw new functions.https.HttpsError('invalid-argument', 'Missing required parameters');
  }

  try {
    // Token expires in 24 hours
    const expirationTimeInSeconds = Math.floor(Date.now() / 1000) + (24 * 3600);
    
    const agoraRole = role === 'publisher' ? RtcRole.PUBLISHER : RtcRole.SUBSCRIBER;
    
    const token = RtcTokenBuilder.buildTokenWithUid(
      APP_ID,
      APP_CERTIFICATE,
      channelName,
      uid,
      agoraRole,
      expirationTimeInSeconds
    );

    return { token, expirationTime: expirationTimeInSeconds };
  } catch (error) {
    console.error('Error generating Agora token:', error);
    throw new functions.https.HttpsError('internal', 'Failed to generate token');
  }
});
```

Install required packages:
```bash
cd functions
npm install agora-access-token
```

### Option B: Express.js Token Server

Create a separate Node.js server:

```javascript
// token-server.js
const express = require('express');
const { RtcTokenBuilder, RtcRole } = require('agora-access-token');
const app = express();

app.use(express.json());

const APP_ID = process.env.AGORA_APP_ID;
const APP_CERTIFICATE = process.env.AGORA_APP_CERTIFICATE;

app.post('/generate-token', (req, res) => {
  const { channelName, uid, role } = req.body;
  
  if (!channelName || !uid) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  const expirationTimeInSeconds = Math.floor(Date.now() / 1000) + (24 * 3600);
  const agoraRole = role === 'publisher' ? RtcRole.PUBLISHER : RtcRole.SUBSCRIBER;
  
  const token = RtcTokenBuilder.buildTokenWithUid(
    APP_ID,
    APP_CERTIFICATE,
    channelName,
    uid,
    agoraRole,
    expirationTimeInSeconds
  );

  res.json({ token, expirationTime: expirationTimeInSeconds });
});

app.listen(3000, () => {
  console.log('Token server running on port 3000');
});
```

## 3. Update Flutter App for Production

### Step 1: Update Agora Config
```dart
// lib/config/agora_config.dart
class AgoraConfig {
  // Your production App ID
  static const String appId = 'your_production_app_id';
  
  // Your token server URL
  static const String tokenServerUrl = 'https://your-domain.com/api/generate-token';
  // OR for Firebase Functions:
  // static const String tokenServerUrl = 'generateAgoraToken'; // Cloud Function name
  
  // Production optimized settings
  static const int videoWidth = 1280;
  static const int videoHeight = 720;
  static const int videoFrameRate = 30;
  static const int videoBitrate = 1000;
  
  // Audio quality
  static const int audioSampleRate = 48000;
  static const int audioChannels = 2;
  
  // Call timeout
  static const int callTimeoutDuration = 45;
}
```

### Step 2: Create Token Service
```dart
// lib/services/token_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:cloud_functions/cloud_functions.dart';
import 'package:tolk/config/agora_config.dart';

class TokenService {
  static Future<String?> generateToken({
    required String channelName,
    required int uid,
    String role = 'publisher',
  }) async {
    try {
      // Option A: Using Firebase Cloud Functions
      final callable = FirebaseFunctions.instance.httpsCallable('generateAgoraToken');
      final result = await callable.call({
        'channelName': channelName,
        'uid': uid,
        'role': role,
      });
      
      return result.data['token'] as String?;
      
      // Option B: Using HTTP endpoint
      /*
      final response = await http.post(
        Uri.parse(AgoraConfig.tokenServerUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'channelName': channelName,
          'uid': uid,
          'role': role,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['token'] as String?;
      }
      */
      
    } catch (e) {
      print('Error generating token: $e');
      return null;
    }
  }
}
```

### Step 3: Update Agora Service
```dart
// lib/services/agora_service.dart (update join methods)
import 'package:tolk/services/token_service.dart';

class AgoraService {
  // ... existing code ...

  Future<void> joinVoiceCall(String channelName, int uid) async {
    // Generate token for production
    final token = await TokenService.generateToken(
      channelName: channelName,
      uid: uid,
      role: 'publisher',
    );
    
    if (token == null) {
      throw Exception('Failed to generate token');
    }
    
    await _engine.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
    await _engine.enableAudio();
    await _engine.disableVideo();
    
    await _engine.joinChannel(
      token: token,
      channelId: channelName,
      uid: uid,
      options: const ChannelMediaOptions(),
    );
  }

  Future<void> joinVideoCall(String channelName, int uid) async {
    // Generate token for production
    final token = await TokenService.generateToken(
      channelName: channelName,
      uid: uid,
      role: 'publisher',
    );
    
    if (token == null) {
      throw Exception('Failed to generate token');
    }
    
    await _engine.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
    await _engine.enableAudio();
    await _engine.enableVideo();
    await _engine.startPreview();
    
    await _engine.joinChannel(
      token: token,
      channelId: channelName,
      uid: uid,
      options: const ChannelMediaOptions(),
    );
  }
}
```

## 4. Production Dependencies

Add to `pubspec.yaml`:
```yaml
dependencies:
  cloud_functions: ^4.7.6  # For Firebase Functions token server
  # OR
  http: ^1.2.2             # For HTTP token server
```

## 5. Security Best Practices

### Environment Variables
Never expose credentials in code. Use environment variables:

```bash
# .env file (DO NOT commit to version control)
AGORA_APP_ID=your_app_id
AGORA_APP_CERTIFICATE=your_app_certificate
```

### Firebase Security Rules
Ensure your Firestore rules are properly configured (already done):
```javascript
match /calls/{callId} {
  allow read, write: if request.auth != null && 
    (request.auth.uid == resource.data.callerId || 
     request.auth.uid == resource.data.receiverId);
}
```

## 6. Performance Optimization

### Video Quality Settings
```dart
// lib/services/agora_service.dart
Future<void> optimizeForMobile() async {
  // Optimize for mobile networks
  await _engine.setVideoEncoderConfiguration(
    const VideoEncoderConfiguration(
      dimensions: VideoDimensions(width: 640, height: 360),
      frameRate: VideoFrameRate.fps15,
      bitrate: VideoOutputOrientationMode.adaptative,
    ),
  );
  
  // Enable hardware acceleration
  await _engine.enableHardwareEarphone(true);
  
  // Set audio profile for voice calls
  await _engine.setAudioProfile(
    profile: AudioProfileType.voiceChat,
    scenario: AudioScenarioType.chatRoom,
  );
}
```

## 7. Monitoring and Analytics

### Step 1: Enable Agora Analytics
1. In Agora Console, go to "Analytics"
2. Enable "Real-time Monitoring"
3. Set up alerts for call quality issues

### Step 2: Add Call Quality Monitoring
```dart
// lib/services/agora_service.dart
void setupQualityMonitoring() {
  _engine.registerEventHandler(
    RtcEngineEventHandler(
      onNetworkQuality: (connection, remoteUid, txQuality, rxQuality) {
        // Log network quality
        print('Network Quality - TX: $txQuality, RX: $rxQuality');
        
        // Send to analytics service
        // FirebaseAnalytics.instance.logEvent(
        //   name: 'call_quality',
        //   parameters: {
        //     'tx_quality': txQuality.index,
        //     'rx_quality': rxQuality.index,
        //   },
        // );
      },
      onRtcStats: (connection, stats) {
        // Log call statistics
        print('Call Stats - Duration: ${stats.duration}s, Users: ${stats.userCount}');
      },
    ),
  );
}
```

## 8. Testing Production Setup

### Step 1: Test Token Generation
```dart
// Test in development
void testTokenGeneration() async {
  final token = await TokenService.generateToken(
    channelName: 'test_channel',
    uid: 12345,
  );
  
  print('Generated token: ${token != null ? 'Success' : 'Failed'}');
}
```

### Step 2: Load Testing
1. Use Agora's testing tools
2. Test with multiple concurrent calls
3. Monitor performance metrics

## 9. Deployment Checklist

### Before Going Live:
- [ ] Agora project in "Secured mode"
- [ ] Token server deployed and secured
- [ ] Environment variables configured
- [ ] Firestore security rules updated
- [ ] Call quality monitoring enabled
- [ ] Error handling implemented
- [ ] Load testing completed
- [ ] Backup token server (optional)

### Production Environment:
- [ ] HTTPS-only token server
- [ ] Rate limiting on token generation
- [ ] Proper error logging
- [ ] Analytics integration
- [ ] User feedback system

## 10. Pricing Considerations

### Agora Pricing (as of 2024):
- **Audio**: ~$0.99 per 1,000 minutes
- **Video SD**: ~$3.99 per 1,000 minutes  
- **Video HD**: ~$8.99 per 1,000 minutes

### Cost Optimization:
1. Use audio-only calls when possible
2. Implement call duration limits
3. Monitor usage in Agora Console
4. Set up billing alerts

## 11. Support and Maintenance

### Monitoring:
- Agora Console dashboard
- Firebase Analytics
- Custom error tracking

### Updates:
- Regular SDK updates
- Token expiration handling
- Performance optimization

Your Agora.io implementation is now production-ready with proper security, monitoring, and scalability!