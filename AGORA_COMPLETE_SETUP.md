# 🚀 Complete Agora.io Setup with Firebase Functions

This is your complete guide to set up Agora.io audio and video calling with Firebase Functions for token generation.

## 📋 What's Implemented

### ✅ Features Ready
- **Audio Calls**: Start/receive audio calls from chat screen
- **Video Calls**: Start/receive video calls with camera controls
- **Token Authentication**: Secure Firebase Functions for token generation
- **Incoming Calls**: Real-time call notifications and handling
- **Call Management**: Complete call lifecycle with Firebase signaling

### 📁 Files Created/Updated

#### Firebase Functions:
- `functions/index.js` - Main functions with `generateAgoraToken`
- `functions/package.json` - Dependencies with `agora-token@2.0.5`
- `functions/.env.example` - Environment template
- `functions/deploy-agora.sh` - Deployment script
- `functions/test-agora-function.js` - Testing utilities

#### Flutter App:
- `lib/config/agora_config.dart` - Configured for Firebase Functions
- `lib/services/token_service.dart` - Token generation service
- `lib/services/agora_service.dart` - Updated with token support
- `lib/services/call_service.dart` - Call management with proper UIDs
- `lib/widgets/incoming_call_listener.dart` - Global call handling
- All call screens updated for production use

#### Documentation:
- `FIREBASE_AGORA_SETUP.md` - Detailed Firebase setup guide
- `AGORA_PRODUCTION_SETUP.md` - Production deployment guide
- `setup-agora-firebase.sh` - Automated setup script

## 🚀 Quick Start (Automated Setup)

### Option 1: Automated Setup (Recommended)
```bash
./setup-agora-firebase.sh
```
This script will:
1. Validate your environment
2. Prompt for Agora credentials
3. Configure everything automatically
4. Deploy Firebase Functions
5. Update Flutter app configuration

### Option 2: Manual Setup

#### Step 1: Get Agora Credentials
1. Go to [Agora Console](https://console.agora.io/)
2. Create project in **"Secured mode: APP ID + Token"**
3. Get your App ID and App Certificate

#### Step 2: Configure Firebase Functions
```bash
cd functions
cp .env.example .env
# Edit .env with your credentials
npm install
```

#### Step 3: Deploy Functions
```bash
firebase functions:config:set \
    agora.app_id="YOUR_APP_ID" \
    agora.app_certificate="YOUR_CERTIFICATE"
firebase deploy --only functions
```

#### Step 4: Update Flutter Config
In `lib/config/agora_config.dart`:
```dart
static const String appId = 'your_actual_app_id';
static const String? tokenServerUrl = 'generateAgoraToken';
```

## 🧪 Testing Your Setup

### Test Firebase Function
```bash
cd functions
firebase functions:shell
# Then run:
generateAgoraToken({channelName: 'test', uid: 12345})
```

Expected response:
```json
{
  "token": "006abc123...",
  "expirationTime": 1640995200,
  "channelName": "test",
  "uid": 12345,
  "role": "publisher"
}
```

### Test Flutter App
1. Run `flutter run`
2. Open any chat conversation
3. Tap call buttons (📞 audio, 📹 video)
4. Check logs for successful token generation

## 🔧 Configuration Details

### Firebase Functions Environment
Variables are set via Firebase Functions config:
- `functions.config().agora.app_id`
- `functions.config().agora.app_certificate`

### Flutter App Configuration
- **Development**: Uses Firebase Functions for token generation
- **Security**: Requires Firebase Authentication
- **Token Expiry**: 24 hours (configurable)

## 🛡️ Security Features

### Authentication
- Firebase Auth required before token generation
- User-specific UID generation for call isolation
- Input validation on all parameters

### Token Security
- Server-side generation only
- No credentials exposed to client
- Automatic expiration handling

## 📊 Architecture

```
Flutter App
    ↓ (Authenticated Request)
Firebase Functions (generateAgoraToken)
    ↓ (App ID + Certificate)
Agora Token Server
    ↓ (Secure Token)
Agora RTC Network
```

## 🚨 Troubleshooting

### Common Issues

#### "Agora credentials not configured"
**Fix**: Run the setup script or manually set Firebase config

#### "User must be authenticated"
**Fix**: Ensure user is logged into Firebase Auth

#### "Token generation failed"
**Fix**: Check Agora Console project is in "Secured mode"

#### Call buttons don't work
**Fix**: Verify `tokenServerUrl` is set to `'generateAgoraToken'`

### Debug Mode
Enable in `functions/index.js`:
```javascript
console.log('Debug - Request:', request.data);
```

## 💰 Cost Estimates

### Firebase Functions
- First 2M calls/month: **Free**
- Additional: **$0.40/million calls**

### Agora.io
- Audio: **~$0.99/1000 minutes**
- Video SD: **~$3.99/1000 minutes**
- Video HD: **~$8.99/1000 minutes**

## 📈 Scaling Considerations

### Performance
- Functions auto-scale with demand
- Token caching on client (24hr expiry)
- Minimal latency with global Firebase edge

### Monitoring
- Firebase Console for function metrics
- Agora Console for call quality
- Custom logging for debugging

## 🔄 Maintenance

### Regular Tasks
- Monitor function performance
- Update dependencies quarterly
- Review Agora usage reports
- Check Firebase billing

### Updates
```bash
cd functions
npm update
firebase deploy --only functions
```

## ✅ Production Checklist

Before going live:
- [ ] Agora project in "Secured mode"
- [ ] Firebase Functions deployed successfully
- [ ] Token generation tested
- [ ] End-to-end calling tested
- [ ] Error handling verified
- [ ] Monitoring configured
- [ ] Security rules reviewed
- [ ] Billing alerts set up

## 🎯 Next Steps

1. **Test thoroughly** with multiple users
2. **Monitor performance** in Firebase Console
3. **Add analytics** for call metrics
4. **Implement call recording** (optional)
5. **Set up monitoring alerts**

## 📚 Resources

- [Firebase Functions Docs](https://firebase.google.com/docs/functions)
- [Agora Token Guide](https://docs.agora.io/en/video-calling/develop/authentication-workflow)
- [Flutter Agora SDK](https://docs.agora.io/en/video-calling/reference/api?platform=flutter)

## 🎉 You're Ready!

Your Agora.io integration with Firebase Functions is complete and production-ready. The setup provides:

- **Secure token authentication**
- **Scalable serverless architecture**
- **Real-time call management**
- **Professional calling experience**

Happy calling! 📞📹