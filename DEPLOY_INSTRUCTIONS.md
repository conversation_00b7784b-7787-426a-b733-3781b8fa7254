# 🚀 Firebase Functions Deployment Instructions

Your Firebase Functions are ready to deploy! Follow these steps to complete the setup.

## 📋 Prerequisites

1. **Firebase CLI installed and logged in**:
   ```bash
   npm install -g firebase-tools
   firebase login
   ```

2. **Agora.io Account with credentials**:
   - Go to [<PERSON><PERSON><PERSON> Console](https://console.agora.io/)
   - Create a project in **"Secured mode: APP ID + Token"**
   - Get your App ID and App Certificate

## 🔧 Step-by-Step Deployment

### Step 1: Configure Agora Credentials

Option A: Use the automated script:
```bash
./setup-agora-firebase.sh
```

Option B: Manual configuration:
```bash
cd functions

# Edit .env file with your actual credentials
nano .env
# Replace:
# AGORA_APP_ID=your_actual_app_id
# AGORA_APP_CERTIFICATE=your_actual_app_certificate
```

### Step 2: Set Firebase Functions Config
```bash
cd functions

# Set the configuration in Firebase
firebase functions:config:set \
    agora.app_id="YOUR_ACTUAL_APP_ID" \
    agora.app_certificate="YOUR_ACTUAL_APP_CERTIFICATE"
```

### Step 3: Deploy Functions
```bash
firebase deploy --only functions
```

Expected output:
```
✔ functions: Finished running predeploy script.
✔ functions[generateAgoraToken(us-central1)]: Successful create operation.
✔ Deploy complete!
```

## ✅ Verify Deployment

### Test the Function
```bash
cd functions
firebase functions:shell
```

Then test:
```javascript
generateAgoraToken({channelName: 'test_channel', uid: 12345, role: 'publisher'})
```

Expected response:
```json
{
  "token": "006abc123...",
  "expirationTime": 1640995200,
  "channelName": "test_channel",
  "uid": 12345,
  "role": "publisher"
}
```

## 🔍 Troubleshooting

### Common Issues

#### ESLint Errors
**Fix**: The test file has been removed. ESLint should pass now.

#### "Missing Agora credentials"
**Fix**: Ensure you've set the Firebase config with real credentials:
```bash
firebase functions:config:get
```

#### Deployment Fails
**Fix**: Check you're in the right directory and logged into Firebase:
```bash
firebase projects:list
firebase use your-project-id
```

## 📱 Flutter App Configuration

After successful deployment, your Flutter app is already configured to use the Firebase Function:

- `lib/config/agora_config.dart` is set to use `'generateAgoraToken'`
- All services are updated to use token authentication
- Ready for production calls!

## 🧪 Test End-to-End

1. Deploy the Firebase Functions (above steps)
2. Run your Flutter app: `flutter run`
3. Open any chat conversation
4. Tap call buttons (📞 audio, 📹 video)
5. Check logs for successful token generation

## 📊 Monitor Performance

After deployment:
- **Firebase Console**: Monitor function calls and errors
- **Agora Console**: Track call quality and usage
- **Logs**: Use `firebase functions:log` for debugging

## 💰 Cost Monitoring

Set up billing alerts in:
- Firebase Console → Usage and billing
- Agora Console → Usage dashboard

## 🎉 Success!

Once deployed, you'll have:
- ✅ Secure token-based authentication
- ✅ Scalable serverless architecture  
- ✅ Production-ready calling system
- ✅ Real-time call management

Your Agora.io integration is ready for production! 🚀