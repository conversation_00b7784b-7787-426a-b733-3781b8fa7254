#!/bin/bash

# Deployment script for Agora Firebase Functions
# This script sets up environment variables and deploys the functions

echo "🚀 Deploying Agora Firebase Functions..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found!"
    echo "📝 Please create a .env file with your Agora credentials:"
    echo "   cp .env.example .env"
    echo "   Then edit .env with your actual Agora App ID and Certificate"
    exit 1
fi

# Load environment variables from .env file
echo "📋 Loading environment variables..."
source .env

# Validate required variables
if [ -z "$AGORA_APP_ID" ] || [ -z "$AGORA_APP_CERTIFICATE" ]; then
    echo "❌ Missing required environment variables!"
    echo "📝 Please check your .env file contains:"
    echo "   AGORA_APP_ID=your_app_id"
    echo "   AGORA_APP_CERTIFICATE=your_certificate"
    exit 1
fi

# Check if variables are placeholder values
if [ "$AGORA_APP_ID" = "your_agora_app_id_here" ] || [ "$AGORA_APP_CERTIFICATE" = "your_agora_app_certificate_here" ]; then
    echo "❌ Please replace placeholder values in .env file with actual Agora credentials"
    exit 1
fi

echo "✅ Environment variables validated"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Set Firebase Functions environment variables
echo "🔧 Setting Firebase Functions environment variables..."
firebase functions:config:set \
    agora.app_id="$AGORA_APP_ID" \
    agora.app_certificate="$AGORA_APP_CERTIFICATE"

# Deploy functions
echo "🚀 Deploying functions..."
firebase deploy --only functions

echo "✅ Deployment complete!"
echo ""
echo "📋 Function deployed successfully:"
echo "  - generateAgoraToken: Generates secure tokens for Agora calls"
echo ""
echo "� Next steps:"
echo "1. Update your Flutter app's AgoraConfig.tokenServerUrl to: 'generateAgoraToken'"
echo "2. Test the token generation function"
echo ""
echo "🧪 Test command:"
echo "firebase functions:shell"
echo "Then run: generateAgoraToken({channelName: 'test', uid: 12345})"