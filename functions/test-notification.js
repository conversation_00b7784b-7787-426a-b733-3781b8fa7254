/**
 * Test script for push notifications
 * This script helps test the notification system by creating test documents
 */

const admin = require("firebase-admin");

// Initialize Firebase Admin
// For production, use environment variables or service account key
admin.initializeApp({
  projectId: "multilingual-chat-app-85bd5",
});

const db = admin.firestore();

/**
 * Test the sendNotification function
 * @param {string} fcmToken - The FCM token of the test device
 * @param {string} userId - The user ID to send notification to
 */
async function testSendNotification(fcmToken, userId) {
  try {
    console.log("🧪 Testing sendNotification function...");

    const testNotification = {
      userId: userId,
      fcmToken: fcmToken,
      title: "Test Notification",
      body: "This is a test message from the notification system",
      data: {
        chatRoomId: "test-chat-room-123",
        senderId: "test-sender-456",
        senderName: "Test Sender",
        messageType: "text",
        type: "new_message",
      },
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      processed: false,
      type: "push_notification",
    };

    const docRef = await db.collection("notifications").add(testNotification);
    console.log("✅ Test notification created with ID:", docRef.id);
    console.log("📱 Check your device for the notification!");

    // Wait a bit and check if it was processed
    setTimeout(async () => {
      const doc = await docRef.get();
      const data = doc.data();
      if (data.processed) {
        console.log("✅ Notification was processed successfully!");
        console.log("📊 FCM Response:", data.fcmResponse);
      } else {
        console.log("⏳ Notification is still being processed...");
      }
    }, 5000);
  } catch (error) {
    console.error("❌ Error testing notification:", error);
  }
}

/**
 * Test the testNotification function
 * @param {string} fcmToken - The FCM token of the test device
 */
async function testTestFunction(fcmToken) {
  try {
    console.log("🧪 Testing testNotification function...");

    const testDoc = {
      fcmToken: fcmToken,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    const docRef = await db.collection("test").add(testDoc);
    console.log("✅ Test document created with ID:", docRef.id);
    console.log("📱 Check your device for the test notification!");
  } catch (error) {
    console.error("❌ Error testing test function:", error);
  }
}

/**
 * Clean up test data
 */
async function cleanupTestData() {
  try {
    console.log("🧹 Cleaning up test data...");

    // Clean up test notifications
    const notifications = await db.collection("notifications")
        .where("type", "==", "push_notification")
        .where("data.type", "==", "new_message")
        .where("data.chatRoomId", "==", "test-chat-room-123")
        .get();

    const batch = db.batch();
    notifications.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    // Clean up test documents
    const testDocs = await db.collection("test").get();
    testDocs.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log("✅ Test data cleaned up successfully!");
  } catch (error) {
    console.error("❌ Error cleaning up test data:", error);
  }
}

/**
 * Get user's FCM token from Firestore
 * @param {string} userId - The user ID
 * @return {Promise<string|null>} The FCM token or null
 */
async function getUserFCMToken(userId) {
  try {
    const userDoc = await db.collection("users").doc(userId).get();
    if (userDoc.exists) {
      const userData = userDoc.data();
      return userData.fcmToken;
    } else {
      console.log("❌ User not found:", userId);
      return null;
    }
  } catch (error) {
    console.error("❌ Error getting user FCM token:", error);
    return null;
  }
}

/**
 * List all users with FCM tokens
 */
async function listUsersWithTokens() {
  try {
    console.log("👥 Users with FCM tokens:");

    const users = await db.collection("users")
        .where("fcmToken", "!=", null)
        .get();

    users.docs.forEach((doc) => {
      const data = doc.data();
      const name = data.name || data.phoneNumber || "Unknown";
      console.log(`   • ${name} (${doc.id})`);
      console.log(`     Token: ${data.fcmToken.substring(0, 20)}...`);
    });
  } catch (error) {
    console.error("❌ Error listing users:", error);
  }
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case "test": {
    const fcmToken = args[1];
    const userId = args[2];
    if (!fcmToken || !userId) {
      console.log("Usage: node test-notification.js test <fcmToken> <userId>");
      process.exit(1);
    }
    testSendNotification(fcmToken, userId);
    break;
  }

  case "test-function": {
    const token = args[1];
    if (!token) {
      console.log("Usage: node test-notification.js test-function <fcmToken>");
      process.exit(1);
    }
    testTestFunction(token);
    break;
  }

  case "cleanup":
    cleanupTestData();
    break;

  case "list-users":
    listUsersWithTokens();
    break;

  case "get-token": {
    const uid = args[1];
    if (!uid) {
      console.log("Usage: node test-notification.js get-token <userId>");
      process.exit(1);
    }
    getUserFCMToken(uid).then((token) => {
      if (token) {
        console.log("FCM Token:", token);
      }
    });
    break;
  }

  default:
    console.log("🧪 Tolk Chat App - Notification Testing Tool");
    console.log("");
    console.log("Available commands:");
    console.log("  test <fcmToken> <userId>     - Test sendNotification function");
    console.log("  test-function <fcmToken>     - Test testNotification function");
    console.log("  cleanup                      - Clean up test data");
    console.log("  list-users                   - List users with FCM tokens");
    console.log("  get-token <userId>           - Get FCM token for user");
    console.log("");
    console.log("Examples:");
    console.log("  node test-notification.js test \"fcm_token_here\" \"user_id_here\"");
    console.log("  node test-notification.js list-users");
    console.log("  node test-notification.js cleanup");
    break;
}

// Keep the process alive for a bit to see results
if (command && command !== "list-users" && command !== "get-token") {
  setTimeout(() => {
    process.exit(0);
  }, 10000);
}
