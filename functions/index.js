const {onDocumentCreated} = require("firebase-functions/v2/firestore");
const {onCall} = require("firebase-functions/v2/https");
const {initializeApp} = require("firebase-admin/app");
const {getMessaging} = require("firebase-admin/messaging");
const {getFirestore} = require("firebase-admin/firestore");
const {RtcTokenBuilder, RtcRole} = require("agora-token");

// Initialize Firebase Admin
initializeApp();

/**
 * Cloud Function to send push notifications when new notifications are created
 * Triggers when a document is created in the 'notifications' collection
 */
exports.sendNotification = onDocumentCreated(
    "notifications/{notificationId}",
    async (event) => {
      try {
        const notification = event.data.data();
        const notificationId = event.params.notificationId;

        console.log(`🔔 Processing notification: ${notificationId}`);
        console.log(`🔔 Notification data:`, notification);

        // Check if already processed
        if (notification.processed) {
          console.log(`🔔 Notification ${notificationId} already processed`);
          return null;
        }

        // Validate required fields
        if (!notification.fcmToken || !notification.title || !notification.body) {
          console.error(`🔔 Missing required fields in notification ${notificationId}`);
          await event.data.ref.update({processed: true, error: "Missing required fields"});
          return null;
        }

        // Prepare the FCM message
        const message = {
          notification: {
            title: notification.title,
            body: notification.body,
          },
          data: {
            // Convert all data values to strings (FCM requirement)
            ...Object.fromEntries(
                Object.entries(notification.data || {}).map(([key, value]) => [
                  key,
                  String(value),
                ]),
            ),
            notificationId: notificationId,
          },
          token: notification.fcmToken,
          android: {
            notification: {
              channelId: "chat_messages",
              priority: "high",
              defaultSound: true,
              defaultVibrateTimings: true,
            },
          },
          apns: {
            payload: {
              aps: {
                alert: {
                  title: notification.title,
                  body: notification.body,
                },
                sound: "default",
                badge: 1,
              },
            },
          },
        };

        console.log(`🔔 Sending FCM message:`, message);

        // Send the notification
        const response = await getMessaging().send(message);
        console.log(`🔔 Successfully sent notification: ${response}`);

        // Mark as processed
        await event.data.ref.update({
          processed: true,
          processedAt: new Date(),
          fcmResponse: response,
        });

        console.log(`🔔 Notification ${notificationId} processed successfully`);
        return response;
      } catch (error) {
        console.error(`🔔 Error processing notification:`, error);

        // Mark as processed with error
        try {
          await event.data.ref.update({
            processed: true,
            processedAt: new Date(),
            error: error.message,
          });
        } catch (updateError) {
          console.error(`🔔 Error updating notification document:`, updateError);
        }

        // Don't throw error to prevent function retries
        return null;
      }
    },
);

/**
 * Cloud Function to clean up old processed notifications
 * Runs daily to remove notifications older than 7 days
 */
exports.cleanupNotifications = onDocumentCreated(
    "cleanup/{docId}",
    async (event) => {
      try {
        console.log("🧹 Starting notification cleanup...");

        const db = getFirestore();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        // Query old processed notifications
        const oldNotifications = await db
            .collection("notifications")
            .where("processed", "==", true)
            .where("processedAt", "<", sevenDaysAgo)
            .limit(500) // Process in batches
            .get();

        if (oldNotifications.empty) {
          console.log("🧹 No old notifications to clean up");
          return null;
        }

        // Delete in batches
        const batch = db.batch();
        oldNotifications.docs.forEach((doc) => {
          batch.delete(doc.ref);
        });

        await batch.commit();
        console.log(`🧹 Cleaned up ${oldNotifications.size} old notifications`);

        return {cleaned: oldNotifications.size};
      } catch (error) {
        console.error("🧹 Error during cleanup:", error);
        return null;
      }
    },
);

/**
 * Cloud Function to handle FCM token updates
 * Updates user documents when FCM tokens change
 */
exports.updateFCMToken = onDocumentCreated(
    "fcm_tokens/{tokenId}",
    async (event) => {
      try {
        const tokenData = event.data.data();
        console.log(`🔑 Processing FCM token update:`, tokenData);

        if (!tokenData.userId || !tokenData.token) {
          console.error("🔑 Missing userId or token in FCM token update");
          return null;
        }

        const db = getFirestore();
        await db.collection("users").doc(tokenData.userId).update({
          fcmToken: tokenData.token,
          lastTokenUpdate: new Date(),
        });

        console.log(`🔑 Updated FCM token for user: ${tokenData.userId}`);
        return {success: true};
      } catch (error) {
        console.error("🔑 Error updating FCM token:", error);
        return null;
      }
    },
);


/**
 * Test function for development
 * Can be called manually to test notification sending
 */
exports.testNotification = onDocumentCreated(
    "test/{testId}",
    async (event) => {
      try {
        console.log("🧪 Test notification function called");

        const testData = event.data.data();
        if (!testData.fcmToken) {
          console.error("🧪 No FCM token provided for test");
          return null;
        }

        const message = {
          notification: {
            title: "Test Notification",
            body: "This is a test notification from Cloud Functions",
          },
          data: {
            type: "test",
            timestamp: String(Date.now()),
          },
          token: testData.fcmToken,
        };

        const response = await getMessaging().send(message);
        console.log("🧪 Test notification sent:", response);

        return {success: true, response};
      } catch (error) {
        console.error("🧪 Test notification error:", error);
        return {success: false, error: error.message};
      }
    },
);

/**
 * Cloud Function to generate Agora RTC tokens
 * Used for secure authentication in audio/video calls
 */
exports.generateAgoraToken = onCall(
    {cors: true}, // Enable CORS for web clients
    async (request) => {
      try {
        // Verify user is authenticated
        if (!request.auth) {
          throw new Error("User must be authenticated");
        }

        const {channelName, uid, role = "publisher"} = request.data;

        // Validate required parameters
        if (!channelName || uid === undefined || uid === null) {
          throw new Error("Missing required parameters: channelName and uid");
        }

        // Agora credentials from environment variables
        const APP_ID = process.env.AGORA_APP_ID;
        const APP_CERTIFICATE = process.env.AGORA_APP_CERTIFICATE;

        if (!APP_ID || !APP_CERTIFICATE) {
          console.error("❌ Agora credentials not configured");
          throw new Error("Agora credentials not configured");
        }

        // Convert string uid to number if needed
        const numericUid = typeof uid === "string" ? parseInt(uid) : uid;
        if (isNaN(numericUid)) {
          throw new Error("Invalid UID format");
        }

        // Token expires in 24 hours
        const expirationTimeInSeconds = Math.floor(Date.now() / 1000) + (24 * 3600);

        // Determine Agora role
        const agoraRole = role === "publisher" ? RtcRole.PUBLISHER : RtcRole.SUBSCRIBER;

        console.log(`📞 Generating Agora token for user: ${request.auth.uid}`);
        console.log(`📞 Channel: ${channelName}, UID: ${numericUid}, Role: ${role}`);

        // Generate the token
        const token = RtcTokenBuilder.buildTokenWithUid(
            APP_ID,
            APP_CERTIFICATE,
            channelName,
            numericUid,
            agoraRole,
            expirationTimeInSeconds,
        );

        console.log(`📞 Token generated successfully for channel: ${channelName}`);

        return {
          token: token,
          expirationTime: expirationTimeInSeconds,
          channelName: channelName,
          uid: numericUid,
          role: role,
        };
      } catch (error) {
        console.error("📞 Error generating Agora token:", error);
        throw new Error(`Failed to generate token: ${error.message}`);
      }
    },
);
