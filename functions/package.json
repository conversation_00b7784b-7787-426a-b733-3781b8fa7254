{"name": "tolk-functions", "description": "Cloud Functions for Tolk Chat App", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "agora-token": "^2.0.5"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0"}, "private": true}