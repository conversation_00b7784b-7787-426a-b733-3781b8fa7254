#!/bin/bash

# Tolk Chat App - Cloud Functions Deployment Script
# This script helps deploy Firebase Cloud Functions for push notifications

set -e  # Exit on any error

echo "🚀 Tolk Chat App - Cloud Functions Deployment"
echo "=============================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if we're in the functions directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the functions directory"
    exit 1
fi

# Check if user is logged in to Firebase
echo "🔐 Checking Firebase authentication..."
if ! firebase projects:list &> /dev/null; then
    echo "❌ Not logged in to Firebase. Please run:"
    echo "   firebase login"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Run linting
echo "🔍 Running ESLint..."
npm run lint

# Check if specific function is provided as argument
FUNCTION_NAME=$1

if [ -n "$FUNCTION_NAME" ]; then
    echo "🎯 Deploying specific function: $FUNCTION_NAME"
    firebase deploy --only functions:$FUNCTION_NAME
else
    echo "🌟 Deploying all functions..."
    firebase deploy --only functions
fi

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Next steps:"
echo "   1. Check Firebase Console for function status"
echo "   2. Monitor function logs: firebase functions:log"
echo "   3. Test notifications by sending a message in the app"
echo ""
echo "🔗 Useful commands:"
echo "   • View logs: firebase functions:log"
echo "   • View specific function logs: firebase functions:log --only sendNotification"
echo "   • Start emulators: firebase emulators:start"
echo ""
echo "🎉 Your push notifications are now live!"
