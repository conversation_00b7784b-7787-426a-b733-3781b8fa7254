# 🔥 Firebase Functions for Agora.io Token Generation

This directory contains Firebase Functions that provide secure token generation for Agora.io audio and video calls.

## 📋 What's Included

### Functions
- **`generateAgoraToken`** - Generates secure RTC tokens for Agora calls
- **`sendNotification`** - Handles push notifications for incoming calls
- **`cleanupNotifications`** - Cleans up old notification data
- **`updateFCMToken`** - Manages FCM token updates

### Configuration Files
- **`index.js`** - Main functions implementation
- **`package.json`** - Dependencies including `agora-token@2.0.5`
- **`.env.example`** - Environment template for Agora credentials
- **`.env`** - Your actual credentials (keep private)

### Deployment Scripts
- **`deploy-agora.sh`** - Automated deployment with validation
- **`README.md`** - This documentation

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
From the project root:
```bash
./setup-agora-firebase.sh
```

### Option 2: Manual Setup
```bash
# 1. Configure credentials
cp .env.example .env
# Edit .env with your Agora App ID and Certificate

# 2. Install dependencies
npm install

# 3. Set Firebase config
firebase functions:config:set \
    agora.app_id="YOUR_APP_ID" \
    agora.app_certificate="YOUR_CERTIFICATE"

# 4. Deploy
firebase deploy --only functions
```

## 🔧 Environment Variables

Required in `.env` file:
```bash
AGORA_APP_ID=your_agora_app_id
AGORA_APP_CERTIFICATE=your_agora_app_certificate
```

These are also set in Firebase Functions config:
- `functions.config().agora.app_id`
- `functions.config().agora.app_certificate`

## 🧪 Testing

### Local Testing
```bash
firebase functions:shell
```

Then run:
```javascript
generateAgoraToken({
  channelName: 'test_channel',
  uid: 12345,
  role: 'publisher'
})
```

Expected response:
```json
{
  "token": "006abc123...",
  "expirationTime": 1640995200,
  "channelName": "test_channel",
  "uid": 12345,
  "role": "publisher"
}
```

### Integration Testing
1. Deploy functions
2. Run Flutter app
3. Make test calls
4. Monitor logs: `firebase functions:log`

## 🔒 Security

### Authentication
- Requires valid Firebase Authentication token
- Validates user permissions before generating tokens

### Input Validation
- Validates `channelName` and `uid` parameters
- Sanitizes all inputs
- Prevents injection attacks

### Token Security
- Server-side generation only
- 24-hour expiration
- No credential exposure to client

## 📊 Monitoring

### Firebase Console
- Function execution logs
- Performance metrics
- Error tracking
- Usage statistics

### Agora Console
- Call quality metrics
- Usage reports
- Billing information

## 💰 Cost Structure

### Firebase Functions
- First 2M invocations/month: **Free**
- Additional: **$0.40 per million**

### Agora.io
- Token generation: **Free**
- Call minutes charged separately

## 🛠️ Maintenance

### Regular Tasks
- Monitor function performance
- Update dependencies quarterly
- Review error logs
- Check usage patterns

### Updates
```bash
npm update
firebase deploy --only functions
```

## 🚨 Troubleshooting

### Common Issues

#### ESLint Errors
```bash
npm run lint
npm run lint -- --fix  # Auto-fix some issues
```

#### Missing Config
```bash
firebase functions:config:get  # Check current config
firebase functions:config:set agora.app_id="YOUR_ID"
```

#### Deployment Fails
```bash
firebase login
firebase use --add  # Select correct project
firebase deploy --only functions --debug
```

### Debug Logging
Add to function for debugging:
```javascript
console.log('Debug - Request:', request.data);
console.log('Debug - Auth:', request.auth);
```

## 📚 Dependencies

### Production
- `firebase-admin@^12.0.0` - Firebase Admin SDK
- `firebase-functions@^5.0.0` - Functions framework
- `agora-token@^2.0.5` - Agora token generation

### Development
- `eslint@^8.15.0` - Code linting
- `eslint-config-google@^0.14.0` - Google style guide

## 🎯 Performance

### Optimization
- Functions cold start: ~500ms
- Token generation: ~100ms
- Auto-scaling with demand
- Global edge deployment

### Monitoring
- Set up alerts for high latency
- Monitor error rates
- Track usage patterns

## ✅ Production Checklist

Before going live:
- [ ] Agora credentials configured
- [ ] Functions deployed successfully
- [ ] Token generation tested
- [ ] Integration tested with Flutter app
- [ ] Error handling verified
- [ ] Monitoring enabled
- [ ] Billing alerts set up

## 🎉 Success!

Your Firebase Functions are now ready to provide secure, scalable token generation for Agora.io calls!

For complete setup instructions, see the main project documentation.
