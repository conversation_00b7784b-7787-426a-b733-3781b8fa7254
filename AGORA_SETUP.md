# Agora.io Audio & Video Calling Setup Guide

This guide will help you set up Agora.io for audio and video calling functionality in your Tolk chat application.

## Prerequisites

1. **Agora.io Account**: Sign up at [Agora.io Console](https://console.agora.io/)
2. **Flutter Development Environment**: Ensure you have Flutter SDK installed
3. **Firebase Project**: Your app should already be configured with Firebase

## Step 1: Create Agora Project

1. Go to [Agora Console](https://console.agora.io/)
2. Click "Create Project"
3. Enter a project name (e.g., "Tolk Chat App")
4. Choose "Secured mode: APP ID + Token" for production or "Testing mode: APP ID" for development
5. Click "Submit"
6. Copy your **App ID** from the project dashboard

## Step 2: Configure App ID

1. Open `lib/config/agora_config.dart`
2. Replace `YOUR_AGORA_APP_ID` with your actual App ID:

```dart
class AgoraConfig {
  static const String appId = 'your_actual_app_id_here';
  // ... rest of the configuration
}
```

## Step 3: Install Dependencies

The required dependencies are already added to `pubspec.yaml`:

- `agora_rtc_engine: ^6.3.2` - Agora SDK for Flutter
- `wakelock_plus: ^1.2.8` - Keep screen awake during calls

Run the following command to install:

```bash
flutter pub get
```

## Step 4: Platform-Specific Setup

### Android Setup

The Android permissions are already configured in `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
```

### iOS Setup

The iOS permissions are already configured in `ios/Runner/Info.plist`:

```xml
<key>NSCameraUsageDescription</key>
<string>Take photos and make video calls to share in your conversations.</string>
<key>NSMicrophoneUsageDescription</key>
<string>Record voice messages and make audio/video calls in your conversations.</string>
```

## Step 5: Token Server (Production Only)

For production apps, you'll need to implement token authentication:

1. Set up an Agora token server (refer to [Agora Token Server documentation](https://docs.agora.io/en/video-calling/develop/authentication-workflow))
2. Update `AgoraConfig.tokenServerUrl` with your server URL
3. Modify the call service to fetch tokens before joining channels

## Features Implemented

### ✅ Audio Calling
- Start audio calls from chat screen
- Answer/decline incoming audio calls
- Mute/unmute microphone
- Speaker toggle
- Call duration tracking
- Call status management

### ✅ Video Calling
- Start video calls from chat screen
- Answer/decline incoming video calls
- Camera on/off toggle
- Switch between front/back camera
- Local and remote video views
- Auto-hide controls during call

### ✅ Call Management
- Incoming call notifications
- Call status synchronization via Firebase
- Automatic call cleanup
- User presence detection

## How to Use

### Making Calls

1. Open any chat conversation
2. Tap the phone icon (📞) for audio call
3. Tap the video icon (📹) for video call

### Receiving Calls

1. Incoming calls will show as overlay dialogs
2. Tap green button to answer
3. Tap red button to decline

## File Structure

```
lib/
├── config/
│   └── agora_config.dart          # Agora configuration
├── services/
│   ├── agora_service.dart         # Agora SDK wrapper
│   └── call_service.dart          # Call management & Firebase integration
├── screens/call/
│   ├── audio_call_screen.dart     # Audio call interface
│   └── video_call_screen.dart     # Video call interface
├── widgets/
│   └── incoming_call_listener.dart # Global incoming call handler
└── screens/chat/
    └── chat_screen.dart           # Updated with call buttons
```

## Firestore Database Structure

Calls are stored in the `calls` collection:

```json
{
  "callId": "call_1234567890_1234",
  "callerId": "user_id_1",
  "callerName": "John Doe",
  "callerAvatar": "https://...",
  "receiverId": "user_id_2",
  "type": "audio|video",
  "channelName": "channel_1234567890_1234",
  "timestamp": "2024-01-01T12:00:00Z",
  "status": "calling|answered|ended|declined"
}
```

## Security Rules

Add these Firestore security rules for the calls collection:

```javascript
// Firestore Security Rules
match /calls/{callId} {
  allow read, write: if request.auth != null && 
    (resource.data.callerId == request.auth.uid || 
     resource.data.receiverId == request.auth.uid);
}
```

## Troubleshooting

### Common Issues

1. **"App ID is invalid"**: Ensure you've correctly copied the App ID from Agora Console
2. **Camera/Microphone not working**: Check permissions in device settings
3. **No incoming calls**: Verify Firebase configuration and network connectivity
4. **Poor call quality**: Check network connection and consider adjusting video quality settings

### Debug Mode

For development, you can enable debug logging in `AgoraService`:

```dart
// Add this in initialize() method
await _engine.setParameters('{"rtc.log_filter": 65535}');
```

### Performance Optimization

For better performance, consider:

1. Lowering video resolution for mobile data calls
2. Implementing adaptive bitrate based on network conditions
3. Adding network quality indicators
4. Implementing call recording (requires additional setup)

## Support

- [Agora.io Documentation](https://docs.agora.io/)
- [Flutter SDK Reference](https://docs.agora.io/en/video-calling/reference/api?platform=flutter)
- [GitHub Issues](https://github.com/AgoraIO-Extensions/Agora-Flutter-SDK/issues)

## License

This implementation follows Agora.io's licensing terms. Ensure compliance with their usage policies for production applications.