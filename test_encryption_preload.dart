import 'package:flutter/material.dart';
import 'package:tolk/services/encryption_service.dart';
import 'package:tolk/services/chat_service.dart';

/// Test script to verify encryption key preloading functionality
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔐 Testing encryption key preloading functionality...');
  
  final encryptionService = EncryptionService();
  final chatService = ChatService();
  
  // Initialize services
  encryptionService.initialize();
  
  // Test chat room ID (replace with actual encrypted chat room ID)
  const testChatRoomId = 'test_encrypted_chat_room';
  
  try {
    print('🔐 Step 1: Checking if chat room is encrypted...');
    
    // This would normally get chat room from Firestore
    // For testing, we'll simulate an encrypted chat room
    final isEncrypted = await encryptionService.isChatRoomEncrypted(testChatRoomId);
    print('🔐 Chat room encrypted status: $isEncrypted');
    
    if (isEncrypted) {
      print('🔐 Step 2: Pre-loading encryption key...');
      
      final keyLoaded = await encryptionService.loadChatRoomKey(testChatRoomId);
      
      if (keyLoaded) {
        print('✅ Encryption key loaded successfully!');
        
        // Test encryption/decryption
        const testMessage = 'Hello, this is a test encrypted message!';
        print('🔐 Step 3: Testing encryption with message: "$testMessage"');
        
        final encryptedMessage = encryptionService.encryptMessage(testChatRoomId, testMessage);
        if (encryptedMessage != null) {
          print('✅ Message encrypted successfully: ${encryptedMessage.substring(0, 50)}...');
          
          final decryptedMessage = encryptionService.decryptMessage(testChatRoomId, encryptedMessage);
          if (decryptedMessage == testMessage) {
            print('✅ Message decrypted successfully: "$decryptedMessage"');
            print('🎉 Encryption preloading test PASSED!');
          } else {
            print('❌ Decryption failed. Expected: "$testMessage", Got: "$decryptedMessage"');
          }
        } else {
          print('❌ Encryption failed');
        }
      } else {
        print('❌ Failed to load encryption key');
      }
    } else {
      print('ℹ️ Chat room is not encrypted, no key needed');
    }
  } catch (e) {
    print('❌ Error during encryption test: $e');
  }
  
  print('🔐 Encryption preloading test completed.');
}
