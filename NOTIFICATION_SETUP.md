# Push Notifications Setup Guide

This guide explains how to set up push notifications for the chat application.

## Overview

The notification system consists of:
1. **Client-side**: Flutter app with Firebase Cloud Messaging (FCM) and local notifications
2. **Server-side**: Cloud function or backend service to send push notifications
3. **Database**: Firestore to queue notifications and store FCM tokens

## Current Implementation

### ✅ Client-Side (Completed)
- FCM token generation and storage
- Local notification display
- Notification permission handling
- Background message handling
- Notification queuing in Firestore

### 🔄 Server-Side (Needs Implementation)
A cloud function or backend service is needed to:
- Monitor the `notifications` collection in Firestore
- Send actual push notifications using FCM Admin SDK
- Mark notifications as processed

## Server-Side Implementation Options

### Option 1: Firebase Cloud Function (Recommended)

Create a cloud function that triggers when new notifications are added:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.sendNotification = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    const notification = snap.data();
    
    if (notification.processed) {
      return null;
    }

    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: notification.data,
      token: notification.fcmToken,
    };

    try {
      await admin.messaging().send(message);
      
      // Mark as processed
      await snap.ref.update({ processed: true });
      
      console.log('Notification sent successfully');
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  });
```

### Option 2: Backend Service

Create a backend service (Node.js, Python, etc.) that:
1. Polls the `notifications` collection
2. Sends notifications using FCM Admin SDK
3. Updates the `processed` field

## Setup Steps

### 1. Install Dependencies
```bash
flutter pub get
```

### 2. Configure Firebase
- Ensure FCM is enabled in Firebase Console
- Download and add `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)

### 3. Android Configuration
Add to `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### 4. iOS Configuration
Add to `ios/Runner/Info.plist`:
```xml
<key>UIBackgroundModes</key>
<array>
    <string>remote-notification</string>
</array>
```

### 5. Deploy Cloud Function (Option 1)
```bash
cd functions
npm install
firebase deploy --only functions
```

## How It Works

### Message Flow
1. User sends a message
2. `ChatService.sendMessage()` calls `_sendNotificationsToParticipants()`
3. Notification data is stored in Firestore `notifications` collection
4. Cloud function triggers and sends push notification
5. Recipient receives notification

### Notification Types
- **Text Messages**: Shows message content (truncated if long)
- **Media Messages**: Shows type-specific text (📷 Photo, 🎥 Video, etc.)
- **Voice Messages**: Shows "🎵 Voice message"
- **Location**: Shows "📍 Location"

### Data Structure
Notifications stored in Firestore contain:
```json
{
  "userId": "recipient_user_id",
  "fcmToken": "fcm_token_string",
  "title": "Sender Name",
  "body": "Message content or type",
  "data": {
    "chatRoomId": "chat_room_id",
    "senderId": "sender_user_id",
    "senderName": "Sender Name",
    "messageType": "text|image|video|audio|file|location",
    "type": "new_message"
  },
  "timestamp": "server_timestamp",
  "processed": false,
  "type": "push_notification"
}
```

## Testing

### Test Notification Flow
1. Send a message from one user to another
2. Check Firestore `notifications` collection for new entries
3. Verify cloud function processes the notification
4. Confirm recipient receives push notification

### Debug Logs
- Client logs: Look for `🔔 [NOTIFICATION]` prefixed messages
- Server logs: Check cloud function logs in Firebase Console

## Troubleshooting

### Common Issues
1. **No FCM token**: User hasn't granted notification permissions
2. **Token not saved**: Check user document in Firestore for `fcmToken` field
3. **Notifications not sent**: Verify cloud function is deployed and working
4. **App not receiving**: Check device notification settings

### Debug Steps
1. Check notification permissions in device settings
2. Verify FCM token is saved in user document
3. Check `notifications` collection for queued notifications
4. Review cloud function logs for errors
5. Test with Firebase Console's messaging tool

## Security Notes

- FCM tokens are stored in user documents
- Cloud function has admin privileges
- Notification data should not contain sensitive information
- Consider rate limiting to prevent spam

## Future Enhancements

- Notification categories (message, call, etc.)
- Rich notifications with images
- Notification actions (reply, mark as read)
- Push notification analytics
- Notification scheduling
