# Firebase Functions Setup for Agora.io

This guide will help you set up Firebase Functions to generate Agora.io tokens for secure audio and video calling.

## 🚀 Quick Setup

### Step 1: Get Agora Credentials

1. Go to [Agora Console](https://console.agora.io/)
2. Create a new project or select existing one
3. **Important**: Choose "Secured mode: APP ID + Token" for production
4. Copy your **App ID** and **App Certificate**

### Step 2: Configure Environment Variables

1. Navigate to the functions directory:
   ```bash
   cd functions
   ```

2. Create environment file from template:
   ```bash
   cp .env.example .env
   ```

3. Edit `.env` file with your Agora credentials:
   ```bash
   # Replace with your actual credentials
   AGORA_APP_ID=your_app_id_from_agora_console
   AGORA_APP_CERTIFICATE=your_app_certificate_from_agora_console
   ```

### Step 3: Deploy Firebase Functions

1. Install dependencies and deploy:
   ```bash
   ./deploy-agora.sh
   ```

   Or manually:
   ```bash
   npm install
   firebase functions:config:set agora.app_id="YOUR_APP_ID" agora.app_certificate="YOUR_CERTIFICATE"
   firebase deploy --only functions
   ```

2. Wait for deployment to complete ✅

### Step 4: Update Flutter App

The Flutter app is already configured to use Firebase Functions. Just update your Agora App ID:

1. Open `lib/config/agora_config.dart`
2. Replace `YOUR_AGORA_APP_ID` with your actual App ID:
   ```dart
   static const String appId = 'your_actual_app_id_here';
   ```

## 🧪 Testing

### Test Token Generation

1. Open Firebase Functions shell:
   ```bash
   cd functions
   firebase functions:shell
   ```

2. Test the function:
   ```javascript
   generateAgoraToken({channelName: 'test_channel', uid: 12345, role: 'publisher'})
   ```

3. You should see a response with a token:
   ```json
   {
     "token": "006abc123...",
     "expirationTime": 1640995200,
     "channelName": "test_channel",
     "uid": 12345,
     "role": "publisher"
   }
   ```

### Test in Flutter App

1. Run your Flutter app:
   ```bash
   flutter run
   ```

2. Open any chat conversation
3. Tap the call buttons (📞 for audio, 📹 for video)
4. Check logs for token generation success

## 📋 Function Details

### `generateAgoraToken`

**Purpose**: Generates secure Agora RTC tokens for audio/video calls

**Parameters**:
- `channelName` (string): Name of the call channel
- `uid` (number): Unique user identifier
- `role` (string): Either "publisher" or "subscriber" (default: "publisher")

**Returns**:
```json
{
  "token": "string",
  "expirationTime": "number",
  "channelName": "string", 
  "uid": "number",
  "role": "string"
}
```

**Security**: 
- Requires Firebase Authentication
- Validates all input parameters
- Uses environment variables for credentials

## 🔧 Configuration

### Environment Variables

The function uses these environment variables (set via Firebase Functions config):

```bash
# Set via Firebase CLI
firebase functions:config:set \
    agora.app_id="your_app_id" \
    agora.app_certificate="your_certificate"
```

### Token Expiration

- Default: 24 hours
- Modify in `functions/index.js`:
  ```javascript
  const expirationTimeInSeconds = Math.floor(Date.now() / 1000) + (24 * 3600);
  ```

## 🛡️ Security Features

### Authentication
- Requires valid Firebase Authentication token
- Validates user permissions before generating tokens

### Input Validation
- Validates required parameters (channelName, uid)
- Sanitizes input data
- Prevents unauthorized access

### Error Handling
- Comprehensive error logging
- Graceful failure responses
- No credential exposure in errors

## 📊 Monitoring

### Firebase Console
- View function logs in Firebase Console > Functions
- Monitor performance and errors
- Track usage metrics

### Logging
The function logs:
- Token generation requests
- User authentication status
- Error details (without sensitive data)
- Performance metrics

### Example Logs
```
📞 Generating Agora token for user: user123
📞 Channel: call_user1_user2_1640995200, UID: 12345, Role: publisher
📞 Token generated successfully for channel: call_user1_user2_1640995200
```

## 🚨 Troubleshooting

### Common Issues

#### 1. "Agora credentials not configured"
**Solution**: Set environment variables correctly
```bash
firebase functions:config:set agora.app_id="your_id" agora.app_certificate="your_cert"
```

#### 2. "User must be authenticated"
**Solution**: Ensure user is logged in to Firebase Auth before making calls

#### 3. "Missing required parameters"
**Solution**: Verify channelName and uid are provided in function call

#### 4. Token generation fails
**Solution**: 
- Check Agora credentials are correct
- Verify Agora project is in "Secured mode"
- Ensure App Certificate is from correct project

### Debug Mode

Enable debug logging in `functions/index.js`:
```javascript
// Add at the top of generateAgoraToken function
console.log('Debug - Request data:', request.data);
console.log('Debug - Auth context:', request.auth);
```

## 💰 Cost Considerations

### Firebase Functions
- First 2M invocations/month: Free
- Additional: $0.40 per million invocations

### Agora.io
- Audio calls: ~$0.99 per 1,000 minutes
- Video SD: ~$3.99 per 1,000 minutes
- Video HD: ~$8.99 per 1,000 minutes

### Optimization Tips
1. Cache tokens on client side until expiration
2. Use audio-only calls when possible
3. Monitor usage in both Firebase and Agora consoles

## 🔄 Updates & Maintenance

### Updating Dependencies
```bash
cd functions
npm update
firebase deploy --only functions
```

### Monitoring Health
- Set up Firebase Alerts for function failures
- Monitor Agora Console for call quality issues
- Regular review of function logs

## 📚 Additional Resources

- [Agora Token Server Guide](https://docs.agora.io/en/video-calling/develop/authentication-workflow)
- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
- [Flutter Agora SDK](https://docs.agora.io/en/video-calling/reference/api?platform=flutter)

## ✅ Production Checklist

Before going live:

- [ ] Agora project in "Secured mode"
- [ ] Environment variables configured
- [ ] Functions deployed successfully
- [ ] Token generation tested
- [ ] Call functionality tested end-to-end
- [ ] Error handling verified
- [ ] Monitoring set up
- [ ] Security rules reviewed

Your Firebase Functions are now ready for production Agora.io token generation! 🎉