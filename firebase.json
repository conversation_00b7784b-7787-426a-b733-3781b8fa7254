{"flutter": {"platforms": {"android": {"default": {"projectId": "multilingual-chat-app-85bd5", "appId": "1:393289530782:android:4696f39ab13ab80a332a8f", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "multilingual-chat-app-85bd5", "appId": "1:393289530782:ios:b0561dbb285bf7ad332a8f", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "multilingual-chat-app-85bd5", "configurations": {"android": "1:393289530782:android:4696f39ab13ab80a332a8f", "ios": "1:393289530782:ios:b0561dbb285bf7ad332a8f"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "ui": {"enabled": true, "port": 4000}, "singleProjectMode": true}}