import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tolk/widgets/encryption_widgets.dart';
import 'package:tolk/models/chat_models.dart';

void main() {
  group('Encryption Widgets Tests', () {
    group('EncryptionStatusIndicator Tests', () {
      testWidgets('should show lock icon when encrypted', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionStatusIndicator(isEncrypted: true, size: 24),
            ),
          ),
        );

        expect(find.byIcon(Icons.lock), findsOneWidget);
        expect(find.byIcon(Icons.lock_open), findsNothing);
      });

      testWidgets('should show unlock icon when not encrypted', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionStatusIndicator(isEncrypted: false, size: 24),
            ),
          ),
        );

        expect(find.byIcon(Icons.lock_open), findsOneWidget);
        expect(find.byIcon(Icons.lock), findsNothing);
      });

      testWidgets('should handle tap when onTap is provided', (
        WidgetTester tester,
      ) async {
        bool tapped = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionStatusIndicator(
                isEncrypted: true,
                size: 24,
                onTap: () {
                  tapped = true;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.byType(EncryptionStatusIndicator));
        expect(tapped, isTrue);
      });

      testWidgets('should not be tappable when onTap is null', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionStatusIndicator(isEncrypted: true, size: 24),
            ),
          ),
        );

        // Should not throw error when tapped without onTap
        await tester.tap(find.byType(EncryptionStatusIndicator));
        await tester.pump();
      });
    });

    group('EncryptionToggle Tests', () {
      testWidgets('should show correct initial state', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionToggle(isEncrypted: true, onChanged: (value) {}),
            ),
          ),
        );

        final switchWidget = tester.widget<Switch>(find.byType(Switch));
        expect(switchWidget.value, isTrue);
      });

      testWidgets('should call onChanged when toggled', (
        WidgetTester tester,
      ) async {
        bool? changedValue;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionToggle(
                isEncrypted: false,
                onChanged: (value) {
                  changedValue = value;
                },
              ),
            ),
          ),
        );

        await tester.tap(find.byType(Switch));
        expect(changedValue, isTrue);
      });

      testWidgets('should be disabled when onChanged is null', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionToggle(isEncrypted: false, onChanged: (value) {}),
            ),
          ),
        );

        final switchWidget = tester.widget<Switch>(find.byType(Switch));
        expect(switchWidget.onChanged, isNull);
      });
    });

    group('EnableEncryptionDialog Tests', () {
      testWidgets('should show dialog with correct title', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => EnableEncryptionDialog(
                                onConfirm: (password) {},
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        expect(find.text('Enable End-to-End Encryption'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Enable'), findsOneWidget);
      });

      testWidgets('should handle password input', (WidgetTester tester) async {
        String? receivedPassword;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => EnableEncryptionDialog(
                                onConfirm: (password) {
                                  receivedPassword = password;
                                },
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Select custom password option
        await tester.tap(find.text('Custom Password'));
        await tester.pumpAndSettle();

        // Enter password
        await tester.enterText(find.byType(TextField), 'test_password_123');

        // Confirm
        await tester.tap(find.text('Enable'));
        await tester.pumpAndSettle();

        expect(receivedPassword, equals('test_password_123'));
      });

      testWidgets('should require password input', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => EnableEncryptionDialog(
                                onConfirm: (password) {},
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Try to enable without entering password
        await tester.tap(find.text('Enable'));
        await tester.pumpAndSettle();

        // Should show error message
        expect(find.text('Please enter a password'), findsOneWidget);
      });

      testWidgets('should validate minimum password length', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => EnableEncryptionDialog(
                                onConfirm: (password) {},
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        // Enter short password
        await tester.enterText(find.byType(TextField), '123');
        await tester.tap(find.text('Enable'));
        await tester.pumpAndSettle();

        // Should show minimum length error
        expect(
          find.text('Password must be at least 6 characters long'),
          findsOneWidget,
        );
      });
    });

    group('DisableEncryptionDialog Tests', () {
      testWidgets('should show confirmation dialog', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => DisableEncryptionDialog(
                                onConfirm: () {},
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        expect(find.text('Disable Encryption'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Disable'), findsOneWidget);
        expect(
          find.textContaining('This will disable end-to-end encryption'),
          findsOneWidget,
        );
      });

      testWidgets('should call onConfirm when confirmed', (
        WidgetTester tester,
      ) async {
        bool confirmed = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Builder(
                builder:
                    (context) => ElevatedButton(
                      onPressed: () {
                        showDialog(
                          context: context,
                          builder:
                              (context) => DisableEncryptionDialog(
                                onConfirm: () {
                                  confirmed = true;
                                },
                                onCancel: () {},
                              ),
                        );
                      },
                      child: Text('Show Dialog'),
                    ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Show Dialog'));
        await tester.pumpAndSettle();

        await tester.tap(find.text('Disable'));
        expect(confirmed, isTrue);
      });
    });

    group('EncryptionKeyShareWidget Tests', () {
      testWidgets('should display password and copy button', (
        WidgetTester tester,
      ) async {
        const testPassword = 'test_password_123';

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionKeyShareWidget(
                chatRoomId: 'test_room',
                encryptionPassword: testPassword,
              ),
            ),
          ),
        );

        expect(find.text(testPassword), findsOneWidget);
        expect(find.byIcon(Icons.copy), findsOneWidget);
        expect(find.text('Copy'), findsOneWidget);
      });

      testWidgets('should show copied confirmation when copy is tapped', (
        WidgetTester tester,
      ) async {
        const testPassword = 'test_password_123';

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionKeyShareWidget(
                chatRoomId: 'test_room',
                encryptionPassword: testPassword,
              ),
            ),
          ),
        );

        // Tap copy button
        await tester.tap(find.text('Copy'));
        await tester.pump();

        // Should show "Copied!" text briefly
        expect(find.text('Copied!'), findsOneWidget);
      });

      testWidgets('should handle empty password', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EncryptionKeyShareWidget(
                chatRoomId: 'test_room',
                encryptionPassword: '',
              ),
            ),
          ),
        );

        expect(find.text(''), findsOneWidget);
        expect(find.byIcon(Icons.copy), findsOneWidget);
      });
    });
  });

  group('Integration Tests', () {
    testWidgets('should integrate encryption status with chat room data', (
      WidgetTester tester,
    ) async {
      final encryptedChatRoom = ChatRoom(
        id: 'test_room',
        participants: ['user1', 'user2'],
        unreadCount: {'user1': 0, 'user2': 0},
        createdAt: DateTime.now(),
        createdBy: 'user1',
        lastMessage: 'Hello',
        lastMessageTime: DateTime.now(),
        isGroupChat: false,
        isEncrypted: true,
        encryptionEnabledAt: DateTime.now(),
        encryptionEnabledBy: 'user1',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                EncryptionStatusIndicator(
                  isEncrypted: encryptedChatRoom.isEncrypted,
                  size: 24,
                ),
                EncryptionToggle(
                  isEncrypted: encryptedChatRoom.isEncrypted,
                  onChanged: (value) {},
                ),
              ],
            ),
          ),
        ),
      );

      // Should show encrypted state
      expect(find.byIcon(Icons.lock), findsOneWidget);

      final switchWidget = tester.widget<Switch>(find.byType(Switch));
      expect(switchWidget.value, isTrue);
    });
  });
}
