import 'package:flutter_test/flutter_test.dart';
import 'package:tolk/services/encryption_service.dart';
import 'package:tolk/models/chat_models.dart';

void main() {
  group('EncryptionService Tests', () {
    late EncryptionService encryptionService;
    
    setUp(() {
      encryptionService = EncryptionService();
    });

    group('Key Generation and Storage', () {
      test('should enable encryption for chat room', () async {
        const chatRoomId = 'test_chat_room_123';
        const participantIds = ['user1', 'user2'];
        const password = 'test_password_123';

        // Enable encryption for chat room
        final result = await encryptionService.enableEncryptionForChatRoom(
          chatRoomId,
          participantIds,
          customPassword: password,
        );

        // Verify encryption was enabled successfully
        expect(result, isTrue);
      });

      test('should disable encryption for chat room', () async {
        const chatRoomId = 'test_chat_room_456';
        const participantIds = ['user1', 'user2'];
        const password = 'test_password';
        
        // Enable encryption first
        await encryptionService.enableEncryptionForChatRoom(
          chatRoomId, 
          participantIds,
          customPassword: password,
        );
        
        // Disable encryption
        final result = await encryptionService.disableEncryptionForChatRoom(chatRoomId);
        expect(result, isTrue);
      });
    });

    group('Message Encryption/Decryption', () {
      test('should encrypt and decrypt text messages correctly', () async {
        const chatRoomId = 'test_chat_encrypt_123';
        const participantIds = ['user1', 'user2'];
        const password = 'encryption_password';
        const originalText = 'Hello, this is a secret message!';
        
        // Setup encryption
        await encryptionService.enableEncryptionForChatRoom(
          chatRoomId, 
          participantIds,
          customPassword: password,
        );
        
        // Encrypt message
        final encryptedText = await encryptionService.encryptMessage(chatRoomId, originalText);
        expect(encryptedText, isNotNull);
        expect(encryptedText, isNot(equals(originalText))); // Should be different
        
        // Decrypt message
        final decryptedText = await encryptionService.decryptMessage(chatRoomId, encryptedText!);
        expect(decryptedText, equals(originalText)); // Should match original
      });

      test('should handle empty messages', () async {
        const chatRoomId = 'test_chat_empty_123';
        const participantIds = ['user1', 'user2'];
        const password = 'test_password';
        
        await encryptionService.enableEncryptionForChatRoom(
          chatRoomId, 
          participantIds,
          customPassword: password,
        );
        
        // Test empty string
        final encryptedEmpty = await encryptionService.encryptMessage(chatRoomId, '');
        expect(encryptedEmpty, isNotNull);
        final decryptedEmpty = await encryptionService.decryptMessage(chatRoomId, encryptedEmpty!);
        expect(decryptedEmpty, equals(''));
      });

      test('should handle special characters and emojis', () async {
        const chatRoomId = 'test_chat_special_123';
        const participantIds = ['user1', 'user2'];
        const password = 'special_password';
        const specialText = 'Hello! 🎉 Special chars: @#\$%^&*()_+';
        
        await encryptionService.enableEncryptionForChatRoom(
          chatRoomId, 
          participantIds,
          customPassword: password,
        );
        
        final encrypted = await encryptionService.encryptMessage(chatRoomId, specialText);
        expect(encrypted, isNotNull);
        
        final decrypted = await encryptionService.decryptMessage(chatRoomId, encrypted!);
        expect(decrypted, equals(specialText));
      });
    });

    group('Error Handling', () {
      test('should handle encryption without key setup', () async {
        const chatRoomId = 'test_chat_no_key';
        const message = 'Test message';
        
        // Try to encrypt without setting up key
        expect(
          () async => await encryptionService.encryptMessage(chatRoomId, message),
          throwsException,
        );
      });

      test('should handle decryption without key setup', () async {
        const chatRoomId = 'test_chat_no_key_decrypt';
        const encryptedMessage = 'fake_encrypted_message';
        
        // Try to decrypt without setting up key
        expect(
          () async => await encryptionService.decryptMessage(chatRoomId, encryptedMessage),
          throwsException,
        );
      });
    });
  });

  group('ChatRoom Model Tests', () {
    test('should create ChatRoom with encryption fields', () {
      final now = DateTime.now();
      final chatRoom = ChatRoom(
        id: 'test_room',
        participants: ['user1', 'user2'],
        unreadCount: {'user1': 0, 'user2': 0},
        createdAt: now,
        createdBy: 'user1',
        lastMessage: 'Hello',
        lastMessageTime: now,
        isGroupChat: false,
        isEncrypted: true,
        encryptionEnabledAt: now,
        encryptionEnabledBy: 'user1',
      );

      expect(chatRoom.isEncrypted, isTrue);
      expect(chatRoom.encryptionEnabledAt, equals(now));
      expect(chatRoom.encryptionEnabledBy, equals('user1'));
      expect(chatRoom.encryptionDisabledAt, isNull);
      expect(chatRoom.encryptionDisabledBy, isNull);
    });
  });

  group('Message Model Tests', () {
    test('should create Message with encryption fields', () {
      final now = DateTime.now();
      final message = Message(
        id: 'test_message',
        senderId: 'user1',
        chatRoomId: 'test_room',
        type: MessageType.text,
        status: MessageStatus.sent,
        timestamp: now,
        readBy: [],
        text: null, // Original text should be null for encrypted messages
        isEncrypted: true,
        encryptedText: 'encrypted_content_here',
      );

      expect(message.isEncrypted, isTrue);
      expect(message.text, isNull);
      expect(message.encryptedText, equals('encrypted_content_here'));
      expect(message.getDisplayText(), equals('encrypted_content_here'));
    });

    test('should handle non-encrypted messages correctly', () {
      final now = DateTime.now();
      final message = Message(
        id: 'test_message',
        senderId: 'user1',
        chatRoomId: 'test_room',
        type: MessageType.text,
        status: MessageStatus.sent,
        timestamp: now,
        readBy: [],
        text: 'Plain text message',
        isEncrypted: false,
      );

      expect(message.isEncrypted, isFalse);
      expect(message.text, equals('Plain text message'));
      expect(message.encryptedText, isNull);
      expect(message.getDisplayText(), equals('Plain text message'));
    });
  });
}
