class Language {
  final String name;
  final String code;

  const Language({
    required this.name,
    required this.code,
  });

  factory Language.fromJson(Map<String, dynamic> json) {
    return Language(
      name: json['language'] as String,
      code: json['code'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'language': name,
      'code': code,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Language && other.code == code && other.name == name;
  }

  @override
  int get hashCode => Object.hash(name, code);

  @override
  String toString() => 'Language(name: $name, code: $code)';
}

class LanguageData {
  final List<Language> textLanguages;
  final List<Language> ttsLanguages;

  const LanguageData({
    required this.textLanguages,
    required this.ttsLanguages,
  });

  factory LanguageData.fromJson(Map<String, dynamic> json) {
    return LanguageData(
      textLanguages: (json['text'] as List<dynamic>)
          .map((e) => Language.fromJson(e as Map<String, dynamic>))
          .toList(),
      ttsLanguages: (json['tts'] as List<dynamic>)
          .map((e) => Language.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': textLanguages.map((e) => e.toJson()).toList(),
      'tts': ttsLanguages.map((e) => e.toJson()).toList(),
    };
  }
}
