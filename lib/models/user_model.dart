import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String uid;
  final String phoneNumber;
  final String? name;
  final String? profilePicture;
  final String? status;
  final bool isOnline;
  final DateTime? lastSeen;
  final DateTime createdAt;
  final String translationLanguage;
  final bool contactsImported;

  UserModel({
    required this.uid,
    required this.phoneNumber,
    this.name,
    this.profilePicture,
    this.status,
    this.isOnline = false,
    this.lastSeen,
    required this.createdAt,
    this.translationLanguage = 'en', // Default to English
    this.contactsImported = false, // Default to false
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      name: map['name'],
      profilePicture: map['profilePicture'],
      status: map['status'],
      isOnline: map['isOnline'] ?? false,
      lastSeen:
          map['lastSeen'] != null
              ? (map['lastSeen'] as Timestamp).toDate()
              : null,
      createdAt:
          map['createdAt'] != null
              ? (map['createdAt'] as Timestamp).toDate()
              : DateTime.now(),
      translationLanguage: map['translationLanguage'] ?? 'en',
      contactsImported: map['contactsImported'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'phoneNumber': phoneNumber,
      'name': name,
      'profilePicture': profilePicture,
      'status': status,
      'isOnline': isOnline,
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'translationLanguage': translationLanguage,
      'contactsImported': contactsImported,
    };
  }

  UserModel copyWith({
    String? name,
    String? profilePicture,
    String? status,
    bool? isOnline,
    DateTime? lastSeen,
    String? translationLanguage,
    bool? contactsImported,
  }) {
    return UserModel(
      uid: uid,
      phoneNumber: phoneNumber,
      name: name ?? this.name,
      profilePicture: profilePicture ?? this.profilePicture,
      status: status ?? this.status,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      createdAt: createdAt,
      translationLanguage: translationLanguage ?? this.translationLanguage,
      contactsImported: contactsImported ?? this.contactsImported,
    );
  }
}
