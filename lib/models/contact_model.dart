class ContactModel {
  final String id;
  final String displayName;
  final List<String> phoneNumbers;
  final String? profilePicture;
  final bool isRegistered;
  final String? userId; // Firebase user ID if registered

  const ContactModel({
    required this.id,
    required this.displayName,
    required this.phoneNumbers,
    this.profilePicture,
    this.isRegistered = false,
    this.userId,
  });

  factory ContactModel.fromJson(Map<String, dynamic> json) {
    return ContactModel(
      id: json['id'] as String,
      displayName: json['displayName'] as String,
      phoneNumbers: List<String>.from(json['phoneNumbers'] as List),
      profilePicture: json['profilePicture'] as String?,
      isRegistered: json['isRegistered'] as bool? ?? false,
      userId: json['userId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'displayName': displayName,
      'phoneNumbers': phoneNumbers,
      'profilePicture': profilePicture,
      'isRegistered': isRegistered,
      'userId': userId,
    };
  }

  ContactModel copyWith({
    String? id,
    String? displayName,
    List<String>? phoneNumbers,
    String? profilePicture,
    bool? isRegistered,
    String? userId,
  }) {
    return ContactModel(
      id: id ?? this.id,
      displayName: displayName ?? this.displayName,
      phoneNumbers: phoneNumbers ?? this.phoneNumbers,
      profilePicture: profilePicture ?? this.profilePicture,
      isRegistered: isRegistered ?? this.isRegistered,
      userId: userId ?? this.userId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ContactModel &&
        other.id == id &&
        other.displayName == displayName &&
        _listEquals(other.phoneNumbers, phoneNumbers) &&
        other.profilePicture == profilePicture &&
        other.isRegistered == isRegistered &&
        other.userId == userId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      displayName,
      phoneNumbers,
      profilePicture,
      isRegistered,
      userId,
    );
  }

  @override
  String toString() {
    return 'ContactModel(id: $id, displayName: $displayName, phoneNumbers: $phoneNumbers, isRegistered: $isRegistered)';
  }

  // Helper method to get the primary phone number
  String? get primaryPhoneNumber {
    return phoneNumbers.isNotEmpty ? phoneNumbers.first : null;
  }

  // Helper method to format phone number for display
  String get formattedPhoneNumber {
    final primary = primaryPhoneNumber;
    if (primary == null) return '';
    
    // Remove all non-digit characters
    final digits = primary.replaceAll(RegExp(r'[^\d]'), '');
    
    // Format based on length (assuming international format)
    if (digits.length >= 10) {
      return '+${digits.substring(0, digits.length - 10)} ${digits.substring(digits.length - 10, digits.length - 7)} ${digits.substring(digits.length - 7, digits.length - 4)} ${digits.substring(digits.length - 4)}';
    }
    
    return primary;
  }
}

// Helper function for list equality
bool _listEquals<T>(List<T>? a, List<T>? b) {
  if (a == null) return b == null;
  if (b == null || a.length != b.length) return false;
  for (int index = 0; index < a.length; index += 1) {
    if (a[index] != b[index]) return false;
  }
  return true;
}
