import 'package:flutter/material.dart';
import '../models/chat_models.dart';
import '../services/chat_service.dart';
import '../services/encryption_service.dart';
import '../widgets/encryption_widgets.dart';

class EncryptionSettingsScreen extends StatefulWidget {
  final ChatRoom chatRoom;
  final VoidCallback? onEncryptionChanged;

  const EncryptionSettingsScreen({
    super.key,
    required this.chatRoom,
    this.onEncryptionChanged,
  });

  @override
  State<EncryptionSettingsScreen> createState() =>
      _EncryptionSettingsScreenState();
}

class _EncryptionSettingsScreenState extends State<EncryptionSettingsScreen> {
  final ChatService _chatService = ChatService();
  final EncryptionService _encryptionService = EncryptionService();

  bool _isLoading = false;
  String? _customPassword;
  String? _currentPassword;
  late bool _isEncrypted; // Local state to track encryption status

  @override
  void initState() {
    super.initState();
    _isEncrypted = widget.chatRoom.isEncrypted; // Initialize with current state
    _loadCurrentPassword();
  }

  Future<void> _loadCurrentPassword() async {
    if (_isEncrypted) {
      try {
        await _encryptionService.loadChatRoomKey(widget.chatRoom.id);
        // Note: In a real implementation, you might want to store and retrieve
        // the original password for display purposes
        setState(() {
          _currentPassword = "••••••••"; // Masked password display
        });
      } catch (e) {
        debugPrint('Error loading encryption password: $e');
      }
    }
  }

  Future<void> _toggleEncryption(bool enable) async {
    setState(() {
      _isLoading = true;
    });

    try {
      bool success;
      if (enable) {
        success = await _chatService.enableChatRoomEncryption(
          widget.chatRoom.id,
          customPassword: _customPassword,
        );
        if (success) {
          setState(() {
            _isEncrypted = true; // Update local state
          });
          await _loadCurrentPassword();

          // Notify parent about encryption change
          widget.onEncryptionChanged?.call();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Encryption enabled successfully'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      } else {
        success = await _chatService.disableChatRoomEncryption(
          widget.chatRoom.id,
        );
        if (success) {
          setState(() {
            _isEncrypted = false; // Update local state
            _currentPassword = null;
          });

          // Notify parent about encryption change
          widget.onEncryptionChanged?.call();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Encryption disabled'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        }
      }

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to ${enable ? 'enable' : 'disable'} encryption',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showEnableEncryptionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => EnableEncryptionDialog(
            onConfirm: (password) {
              Navigator.of(context).pop();
              _customPassword = password;
              _toggleEncryption(true);
            },
            onCancel: () => Navigator.of(context).pop(),
            onPasswordChanged: (password) {
              _customPassword = password;
            },
          ),
    );
  }

  void _showDisableEncryptionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => DisableEncryptionDialog(
            onConfirm: () {
              Navigator.of(context).pop();
              _toggleEncryption(false);
            },
            onCancel: () => Navigator.of(context).pop(),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Encryption Settings'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Encryption Status Card
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      EncryptionStatusIndicator(
                        isEncrypted: _isEncrypted,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        _isEncrypted
                            ? 'Encryption Enabled'
                            : 'Encryption Disabled',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isEncrypted
                        ? 'Messages in this chat are end-to-end encrypted'
                        : 'Messages in this chat are not encrypted',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  if (_isEncrypted &&
                      widget.chatRoom.encryptionEnabledAt != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Enabled on ${_formatDate(widget.chatRoom.encryptionEnabledAt!)}',
                      style: TextStyle(color: Colors.grey[500], fontSize: 12),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Encryption Toggle
          Card(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            child: EncryptionToggle(
              isEncrypted: _isEncrypted,
              isLoading: _isLoading,
              onChanged: (enable) {
                if (enable) {
                  _showEnableEncryptionDialog();
                } else {
                  _showDisableEncryptionDialog();
                }
              },
              title: 'End-to-End Encryption',
              subtitle:
                  _isEncrypted
                      ? 'Tap to disable encryption'
                      : 'Tap to enable encryption',
            ),
          ),

          // Encryption Key Share Widget (only show if encrypted)
          if (_isEncrypted && _currentPassword != null)
            EncryptionKeyShareWidget(
              chatRoomId: widget.chatRoom.id,
              encryptionPassword: _currentPassword,
            ),

          // Information Section
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'About Encryption',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '• End-to-end encryption protects your messages from being read by anyone except the participants',
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Only messages sent after enabling encryption will be encrypted',
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• All participants need the encryption password to read encrypted messages',
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• Keep your encryption password secure and share it only with trusted participants',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
