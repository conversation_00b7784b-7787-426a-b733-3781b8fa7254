import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:tolk/services/agora_service.dart';
import 'package:tolk/services/call_service.dart'; // Import CallService
import 'package:tolk/utils/app_colors.dart';
import 'dart:async';
import 'package:provider/provider.dart'; // Import Provider
import 'package:tolk/providers/call_provider.dart'; // Import CallProvider
import 'package:tolk/screens/chat/chat_list_screen.dart'; // TODO: Verify this import path

class VideoCallScreen extends StatefulWidget {
  final String channelName;
  final int uid;
  final String callerName;
  final String? callerAvatar;
  final bool isIncoming;
  final String callId; // Added callId
  final bool launchedFromNotification; // To track how the screen was opened

  const VideoCallScreen({
    super.key,
    required this.channelName,
    required this.uid,
    required this.callerName,
    this.callerAvatar,
    this.isIncoming = false,
    required this.callId, // Added callId to constructor
    this.launchedFromNotification = false, // Default to false
  });

  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen> {
  final AgoraService _agoraService = AgoraService();
  final CallService _callService = CallService(); // Add CallService instance
  bool _isCallConnected = false;
  bool _isMuted = false;
  bool _isVideoMuted = false;
  Timer? _callTimer;
  int _callDuration = 0;
  bool _isCallAnswered = false;
  bool _showControls = true;
  Timer? _controlsTimer;
  StreamSubscription<CallData?>? _callStatusSubscription;
  bool _isScreenDismissed = false; // To prevent multiple dismissals
  bool _isPreviewing = false; // To track if local preview is active

  @override
  void initState() {
    super.initState();
    _initializeCall();
    WakelockPlus.enable(); // Keep screen awake during call
    _startControlsTimer();

    _callStatusSubscription = _callService.listenForCallStatus(widget.callId).listen((callData) {
      if (callData == null ||
          callData.status == 'ended' ||
          callData.status == 'declined' ||
          callData.status == 'timeout') {
        _handleCallScreenDismiss();
      }
       // Update _isCallAnswered based on stream if needed
      if (callData != null && callData.status == 'answered' && !_isCallAnswered) {
        if (mounted) {
          setState(() {
            _isCallAnswered = true;
             // If it's an incoming call and now answered by stream, and local user hasn't joined Agora yet
            // but timer hasn't started, start it.
            if (widget.isIncoming && _isCallConnected && _callTimer == null) {
               _startCallTimer();
            }
          });
        }
      }
    });

    // Ensure call screen status is correctly set in provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
       Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(true);
      }
    });
  }

  @override
  void dispose() {
    _callStatusSubscription?.cancel();
    _callTimer?.cancel();
    _controlsTimer?.cancel();
    _agoraService.dispose(); // This should also leave the channel if not already left
    WakelockPlus.disable();
    
    // Reset call screen status when disposed, only if not already handled by _handleCallScreenDismiss
     if (!_isScreenDismissed && mounted) {
       Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(false);
    }
    super.dispose();
  }

  void _handleCallScreenDismiss() {
   if (!mounted || _isScreenDismissed) return;
   _isScreenDismissed = true; // Mark as dismissed
   print('📞 [VIDEO_CALL_SCREEN] Dismissing screen for call ${widget.callId}. Launched from notification: ${widget.launchedFromNotification}');

   _callTimer?.cancel();
   _controlsTimer?.cancel();
   _agoraService.leaveChannel(); // Ensure Agora channel is left

   // Set call screen as closed in provider
   Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(false);
   WakelockPlus.disable(); // Ensure wakelock is disabled

   // Perform navigation
   WidgetsBinding.instance.addPostFrameCallback((_) {
     if (!mounted) return;

     if (widget.launchedFromNotification) {
       print('📞 [VIDEO_CALL_SCREEN] Navigating to ChatListScreen.');
       Navigator.of(context).pushAndRemoveUntil(
         MaterialPageRoute(builder: (context) => const ChatListScreen()),
         (Route<dynamic> route) => false,
       );
     } else {
       print('📞 [VIDEO_CALL_SCREEN] Popping screen.');
       if (Navigator.canPop(context)) {
         Navigator.of(context).pop();
       }
     }
   });
 }

  Future<void> _initializeCall() async {
    await _agoraService.initialize(); // Initialize Agora first

    // If this is an outgoing call, mark it as locally active and start preview.
    if (!widget.isIncoming) {
      print('📞 [VIDEO_CALL_SCREEN] Outgoing call. Setting call ${widget.callId} as locally active in CallService.');
      _callService.setCallLocallyActive(widget.callId);
      try {
        print('📷 [VIDEO_CALL_SCREEN] Starting local preview for outgoing call.');
        await _agoraService.engine.startPreview();
        if (mounted) {
          setState(() {
            _isPreviewing = true;
          });
        }
        print('📷 [VIDEO_CALL_SCREEN] Local preview started.');
      } catch (e) {
        print('📷 [VIDEO_CALL_SCREEN] Error starting local preview: $e');
      }
    }
    
    // Register additional event handlers for call management
    _agoraService.engine.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          if (mounted) {
            setState(() {
              _isCallConnected = true;
            });
          }
          // For incoming calls, timer starts when local user (receiver) joins successfully *after answering*.
          // For outgoing calls, timer starts when remote user (receiver) joins.
          if (widget.isIncoming && _isCallAnswered) {
            _startCallTimer();
          }
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          if (mounted) {
            setState(() {
              _isCallAnswered = true;
            });
          }
          // For outgoing calls, timer starts when the remote user (receiver) joins.
          if (!widget.isIncoming) {
            _startCallTimer();
          }
        },
        onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
          print('📞 [VIDEO_CALL_SCREEN] User $remoteUid offline, reason: $reason');
          _handleCallEnd();
        },
        onLeaveChannel: (RtcConnection connection, RtcStats stats) {
          print('📞 [VIDEO_CALL_SCREEN] Left channel, stats: ${stats.toJson()}');
          _handleCallEnd(isLocalUserLeaving: true);
        },
        onError: (ErrorCodeType err, String msg) {
          print('📞 [VIDEO_CALL_SCREEN] Agora Error: $err, $msg');
          _handleCallEnd(); // Treat errors as call ending
        },
        onConnectionLost: (RtcConnection connection) {
          print('📞 [VIDEO_CALL_SCREEN] Connection Lost');
          _handleCallEnd();
        },
        onConnectionStateChanged: (RtcConnection connection, ConnectionStateType state, ConnectionChangedReasonType reason) {
          print('📞 [VIDEO_CALL_SCREEN] Connection state changed: $state, reason: $reason');
          if (state == ConnectionStateType.connectionStateFailed || state == ConnectionStateType.connectionStateDisconnected) {
            _handleCallEnd();
          }
        },
      ),
    );

    if (!widget.isIncoming) {
      await _joinCall(); // This will also call startPreview in AgoraService, which is fine.
    }
  }

  Future<void> _joinCall() async {
    await _agoraService.joinVideoCall(widget.channelName, widget.uid);
  }

  Future<void> _answerCall() async {
    // First, update the backend status to 'answered'
    try {
      await _callService.answerCall(widget.callId);
      print('📞 [VIDEO_CALL_SCREEN] Call status updated to answered for call ID: ${widget.callId}');
    } catch (e) {
      print('📞 [VIDEO_CALL_SCREEN] Error updating call status to answered: $e');
      // Optionally, handle the error, e.g., by not proceeding or showing a message
      return;
    }

    // Then, join the Agora channel and update local UI state
    await _joinCall();
    if (mounted) {
      setState(() {
        _isCallAnswered = true;
      });
    }
  }

  void _startCallTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _callDuration++;
      });
    });
  }

  void _startControlsTimer() {
    _controlsTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _resetControlsTimer() {
    _controlsTimer?.cancel();
    setState(() {
      _showControls = true;
    });
    _startControlsTimer();
  }

  String _formatCallDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _toggleMute() async {
    await _agoraService.toggleMicrophone();
    setState(() {
      _isMuted = _agoraService.muted;
    });
  }

  Future<void> _toggleVideo() async {
    await _agoraService.toggleCamera();
    setState(() {
      _isVideoMuted = _agoraService.videoMuted;
    });
  }

  Future<void> _switchCamera() async {
    await _agoraService.switchCamera();
  }

  // This method is now primarily for when the *local user* initiates the hangup,
  // or when Agora events indicate a non-recoverable call termination from this device's perspective.
  // The actual screen dismissal is handled by _handleCallScreenDismiss via the Firestore listener.
  void _handleCallEnd({bool isLocalUserLeaving = false}) async {
    print('📞 [VIDEO_CALL_SCREEN] Handling call end logic. Local user leaving: $isLocalUserLeaving. Call ID: ${widget.callId}');

    if (isLocalUserLeaving && !_isScreenDismissed) {
      try {
        print('📞 [VIDEO_CALL_SCREEN] Local user ending call, calling _callService.endCall for ${widget.callId}');
        await _callService.endCall(widget.callId);
      } catch (e) {
        print('📞 [VIDEO_CALL_SCREEN] Error calling _callService.endCall for ${widget.callId}: $e');
        _handleCallScreenDismiss(); // Fallback dismissal
      }
    } else if (!isLocalUserLeaving && !_isScreenDismissed) {
      print('📞 [VIDEO_CALL_SCREEN] Call end triggered by Agora event (not local user). Call ID: ${widget.callId}');
      _callTimer?.cancel();
      _controlsTimer?.cancel();
      _agoraService.leaveChannel();
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        print('📞 [VIDEO_CALL_SCREEN] WillPopScope triggered for call ${widget.callId}.');
        _handleCallEnd(isLocalUserLeaving: true);
        return false; // Let _handleCallScreenDismiss manage navigation.
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          onTap: _resetControlsTimer,
          child: Stack(
            children: [
              // Video views
              _buildVideoViews(),
              
              // Controls overlay
              if (_showControls || (widget.isIncoming && !_isCallAnswered))
                _buildControlsOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoViews() {
    // Case 1: Outgoing call, ringing (not yet answered by remote)
    // Show local preview full screen with "Calling..." overlay.
    if (!widget.isIncoming && !_isCallAnswered && _isPreviewing) {
      return Stack(
        children: [
          // Local video (full screen)
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: _isVideoMuted
                ? Container(
                    color: Colors.black, // Or a placeholder image/icon
                    child: const Center(
                      child: Icon(Icons.videocam_off, color: Colors.white, size: 60),
                    ),
                  )
                : AgoraVideoView(
                    controller: VideoViewController(
                      rtcEngine: _agoraService.engine,
                      canvas: const VideoCanvas(uid: 0), // 0 for local view
                    ),
                  ),
          ),
          // "Calling..." overlay (similar to _buildWaitingView but on top of preview)
          _buildCallingOverlay(),
        ],
      );
    }

    // Case 2: Incoming call, ringing (not yet answered by local user)
    // Show avatar and "Incoming call..."
    if (widget.isIncoming && !_isCallAnswered) {
      return _buildWaitingView(); // Shows avatar and status text
    }

    // Case 3: Call is connected and answered (both incoming and outgoing)
    // Show remote video full screen, local video as PiP.
    // This also covers the brief moment an outgoing call connects but before _isCallAnswered is true from onUserJoined.
    // And the moment an incoming call is answered locally but before _isCallConnected is true from onJoinChannelSuccess.
    if ((_isCallConnected && _isCallAnswered) || (_isPreviewing && !widget.isIncoming)) {
         return Stack(
            children: [
              // Remote video (full screen)
              SizedBox(
                width: double.infinity,
                height: double.infinity,
                child: _agoraService.remoteUid != null
                    ? AgoraVideoView(
                        controller: VideoViewController.remote(
                          rtcEngine: _agoraService.engine,
                          canvas: VideoCanvas(uid: _agoraService.remoteUid),
                          connection: RtcConnection(channelId: widget.channelName),
                        ),
                      )
                    : Container( // Placeholder if remote user hasn't joined or their video is off
                        color: Colors.grey[900],
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircleAvatar(
                                radius: 50,
                                backgroundColor: Colors.grey[700],
                                backgroundImage: widget.callerAvatar != null
                                    ? NetworkImage(widget.callerAvatar!)
                                    : null,
                                child: widget.callerAvatar == null
                                    ? const Icon(Icons.person, size: 50, color: Colors.white)
                                    : null,
                              ),
                              const SizedBox(height: 10),
                              Text(
                                widget.callerName, // This is the remote user's name
                                style: const TextStyle(color: Colors.white, fontSize: 18),
                              ),
                               const SizedBox(height: 5),
                              if (!_isCallAnswered && !widget.isIncoming) // Show "Calling..." if outgoing and not yet answered by remote
                                Text(
                                  _getCallStatusText(),
                                  style: const TextStyle(color: Colors.white70, fontSize: 16),
                                ),
                            ],
                          ),
                        ),
                      ),
              ),
              
              // Local video (small view in corner) - always show if previewing or call is active
              if (_isPreviewing || _isCallAnswered)
                Positioned(
                  top: 50,
                  right: 20,
                  child: Container(
                    width: 120,
                    height: 160,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white.withOpacity(0.5), width: 1),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child: _isVideoMuted
                          ? Container(
                              color: Colors.black54,
                              child: const Center(
                                child: Icon(Icons.videocam_off, color: Colors.white, size: 30),
                              ),
                            )
                          : AgoraVideoView(
                              controller: VideoViewController(
                                rtcEngine: _agoraService.engine,
                                canvas: const VideoCanvas(uid: 0), // 0 for local view
                              ),
                            ),
                    ),
                  ),
                ),
            ],
          );
    }
    
    // Fallback: Still show waiting view if none of the above conditions are met
    // This handles initial state before _isPreviewing is true for outgoing, or before _isCallConnected for incoming.
    return _buildWaitingView();
  }


  // Overlay for outgoing call, showing "Calling..." and recipient info on top of local preview
  Widget _buildCallingOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withOpacity(0.3), // Semi-transparent overlay
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 80,
            backgroundColor: Colors.grey[700]?.withOpacity(0.5),
            backgroundImage: widget.callerAvatar != null // This is the person being called
                ? NetworkImage(widget.callerAvatar!)
                : null,
            child: widget.callerAvatar == null
                ? Icon(Icons.person, size: 80, color: Colors.white.withOpacity(0.8))
                : null,
          ),
          const SizedBox(height: 20),
          Text(
            widget.callerName, // This is the person being called
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              shadows: [Shadow(blurRadius: 2.0, color: Colors.black54)],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            _getCallStatusText(), // Should show "Calling..."
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              shadows: const [Shadow(blurRadius: 1.0, color: Colors.black38)],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }


  Widget _buildWaitingView() {
    // For incoming calls before answered, or initial state for outgoing before preview starts
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.splashColor, // Solid background
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 80,
            backgroundColor: Colors.grey[700],
            backgroundImage: widget.callerAvatar != null // For incoming, this is the caller. For outgoing, this is the person being called.
                ? NetworkImage(widget.callerAvatar!)
                : null,
            child: widget.callerAvatar == null
                ? const Icon(Icons.person, size: 80, color: Colors.white)
                : null,
          ),
          const SizedBox(height: 20),
          Text(
            widget.callerName, // For incoming, this is the caller. For outgoing, this is the person being called.
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            _getCallStatusText(),
            style: const TextStyle(color: Colors.white70, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildControlsOverlay() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Top bar with call info
            if (_isCallConnected && _isCallAnswered)
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Text(
                      widget.callerName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatCallDuration(_callDuration),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            
            const Spacer(),
            
            // Bottom controls
            Container(
              padding: const EdgeInsets.all(20),
              child: widget.isIncoming && !_isCallAnswered
                  ? _buildIncomingCallControls()
                  : _buildCallControls(),
            ),
          ],
        ),
      ),
    );
  }

  String _getCallStatusText() {
    if (widget.isIncoming && !_isCallAnswered) {
      return 'Incoming video call...';
    } else if (!_isCallConnected) {
      return 'Connecting...';
    } else if (!_isCallAnswered) {
      return 'Calling...';
    } else {
      return _formatCallDuration(_callDuration);
    }
  }

  Widget _buildIncomingCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Decline button
        Container(
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () => _handleCallEnd(isLocalUserLeaving: true),
            icon: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
        
        // Answer button
        Container(
          decoration: const BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _answerCall,
            icon: const Icon(
              Icons.videocam,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
      ],
    );
  }

  Widget _buildCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Mute button
        Container(
          decoration: BoxDecoration(
            color: _isMuted ? Colors.red : Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _toggleMute,
            icon: Icon(
              _isMuted ? Icons.mic_off : Icons.mic,
              color: Colors.white,
            ),
            iconSize: 50,
          ),
        ),
        
        // End call button
        Container(
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () => _handleCallEnd(isLocalUserLeaving: true),
            icon: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
        
        // Video toggle button
        Container(
          decoration: BoxDecoration(
            color: _isVideoMuted ? Colors.red : Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _toggleVideo,
            icon: Icon(
              _isVideoMuted ? Icons.videocam_off : Icons.videocam,
              color: Colors.white,
            ),
            iconSize: 50,
          ),
        ),
        
        // Camera switch button
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _switchCamera,
            icon: const Icon(
              Icons.cameraswitch,
              color: Colors.white,
            ),
            iconSize: 50,
          ),
        ),
      ],
    );
  }
}