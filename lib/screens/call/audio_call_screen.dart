import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:tolk/services/agora_service.dart';
import 'package:tolk/services/call_service.dart'; // Import CallService
import 'package:tolk/utils/app_colors.dart';
import 'dart:async';
import 'package:provider/provider.dart'; // Import Provider
import 'package:tolk/providers/call_provider.dart'; // Import CallProvider
import 'package:tolk/screens/chat/chat_list_screen.dart'; // TODO: Verify this import path


class AudioCallScreen extends StatefulWidget {
  final String channelName;
  final int uid;
  final String callerName;
  final String? callerAvatar;
  final bool isIncoming;
  final String callId; // Added callId
  final bool launchedFromNotification; // To track how the screen was opened

  const AudioCallScreen({
    super.key,
    required this.channelName,
    required this.uid,
    required this.callerName,
    this.callerAvatar,
    this.isIncoming = false,
    required this.callId, // Added callId to constructor
    this.launchedFromNotification = false, // Default to false
  });

  @override
  State<AudioCallScreen> createState() => _AudioCallScreenState();
}

class _AudioCallScreenState extends State<AudioCallScreen> {
  final AgoraService _agoraService = AgoraService();
  final CallService _callService = CallService(); // Add CallService instance
  bool _isCallConnected = false;
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  Timer? _callTimer;
  int _callDuration = 0;
  bool _isCallAnswered = false;
  StreamSubscription<CallData?>? _callStatusSubscription;
  bool _isScreenDismissed = false; // To prevent multiple dismissals

  @override
  void initState() {
    super.initState();
    _initializeCall();
    WakelockPlus.enable(); // Keep screen awake during call
    
    _callStatusSubscription = _callService.listenForCallStatus(widget.callId).listen((callData) {
      if (callData == null ||
          callData.status == 'ended' ||
          callData.status == 'declined' ||
          callData.status == 'timeout') {
        _handleCallScreenDismiss();
      }
      // Update _isCallAnswered based on stream if needed, e.g. if call was answered on another device
      if (callData != null && callData.status == 'answered' && !_isCallAnswered) {
        if (mounted) {
          setState(() {
            _isCallAnswered = true;
            // If it's an incoming call and now answered by stream, and local user hasn't joined Agora yet
            // but timer hasn't started, start it.
            if (widget.isIncoming && _isCallConnected && _callTimer == null) {
               _startCallTimer();
            }
          });
        }
      }
    });

    // Ensure call screen status is correctly set in provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
       Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(true);
      }
    });
  }

  @override
  void dispose() {
    _callStatusSubscription?.cancel();
    _callTimer?.cancel();
    _agoraService.dispose(); // This should also leave the channel if not already left
    WakelockPlus.disable();
    
    // Reset call screen status when disposed, only if not already handled by _handleCallScreenDismiss
    if (!_isScreenDismissed && mounted) {
       Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(false);
    }
    super.dispose();
  }

  void _handleCallScreenDismiss() {
   if (!mounted || _isScreenDismissed) return;
   _isScreenDismissed = true; // Mark as dismissed
   print('📞 [AUDIO_CALL_SCREEN] Dismissing screen for call ${widget.callId}. Launched from notification: ${widget.launchedFromNotification}');

   _callTimer?.cancel();
   _agoraService.leaveChannel(); // Ensure Agora channel is left

   // Set call screen as closed in provider
   // This needs to happen before navigation to avoid issues if the next screen also uses CallProvider
   Provider.of<CallProvider>(context, listen: false).setCallScreenOpen(false);
   WakelockPlus.disable(); // Ensure wakelock is disabled

   // Perform navigation
   // Use a post frame callback to ensure build context is valid and screen is not currently building
   WidgetsBinding.instance.addPostFrameCallback((_) {
     if (!mounted) return; // Check mounted again inside callback

     if (widget.launchedFromNotification) {
       print('📞 [AUDIO_CALL_SCREEN] Navigating to ChatListScreen.');
       Navigator.of(context).pushAndRemoveUntil(
         MaterialPageRoute(builder: (context) => const ChatListScreen()),
         (Route<dynamic> route) => false,
       );
     } else {
       print('📞 [AUDIO_CALL_SCREEN] Popping screen.');
       if (Navigator.canPop(context)) {
         Navigator.of(context).pop();
       }
     }
   });
 }


  Future<void> _initializeCall() async {
    // If this is an outgoing call, mark it as locally active immediately.
    if (!widget.isIncoming) {
      print('📞 [AUDIO_CALL_SCREEN] Outgoing call. Setting call ${widget.callId} as locally active in CallService.');
      _callService.setCallLocallyActive(widget.callId);
    }

    await _agoraService.initialize();
    
    // Register additional event handlers for call management
    _agoraService.engine.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          setState(() {
            _isCallConnected = true;
          });
          // For incoming calls, timer starts when local user (receiver) joins successfully *after answering*.
          // For outgoing calls, timer starts when remote user (receiver) joins.
          if (widget.isIncoming && _isCallAnswered) {
            _startCallTimer();
          }
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          setState(() {
            _isCallAnswered = true;
          });
          // For outgoing calls, timer starts when the remote user (receiver) joins.
          if (!widget.isIncoming) {
            _startCallTimer();
          }
        },
        onUserOffline: (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
          // User dropping off or ending the call
          print('📞 [AUDIO_CALL_SCREEN] User $remoteUid offline, reason: $reason');
          _handleCallEnd();
        },
        onLeaveChannel: (RtcConnection connection, RtcStats stats) {
          // Current user leaves the channel
          print('📞 [AUDIO_CALL_SCREEN] Left channel, stats: ${stats.toJson()}');
          _handleCallEnd(isLocalUserLeaving: true);
        },
        onError: (ErrorCodeType err, String msg) {
          print('📞 [AUDIO_CALL_SCREEN] Agora Error: $err, $msg');
          _handleCallEnd(); // Treat errors as call ending
        },
         onConnectionLost: (RtcConnection connection) {
          print('📞 [AUDIO_CALL_SCREEN] Connection Lost');
          _handleCallEnd();
        },
        onConnectionStateChanged: (RtcConnection connection, ConnectionStateType state, ConnectionChangedReasonType reason) {
          print('📞 [AUDIO_CALL_SCREEN] Connection state changed: $state, reason: $reason');
          if (state == ConnectionStateType.connectionStateFailed || state == ConnectionStateType.connectionStateDisconnected) {
            _handleCallEnd();
          }
        },
      ),
    );

    if (!widget.isIncoming) {
      await _joinCall();
    }
  }

  Future<void> _joinCall() async {
    await _agoraService.joinVoiceCall(widget.channelName, widget.uid);
  }

  Future<void> _answerCall() async {
    // First, update the backend status to 'answered'
    try {
      await _callService.answerCall(widget.callId);
      print('📞 [AUDIO_CALL_SCREEN] Call status updated to answered for call ID: ${widget.callId}');
    } catch (e) {
      print('📞 [AUDIO_CALL_SCREEN] Error updating call status to answered: $e');
      // Optionally, handle the error, e.g., by not proceeding or showing a message
      return;
    }

    // Then, join the Agora channel and update local UI state
    await _joinCall();
    if (mounted) {
      setState(() {
        _isCallAnswered = true;
      });
    }
  }

  void _startCallTimer() {
    _callTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _callDuration++;
      });
    });
  }

  String _formatCallDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _toggleMute() async {
    await _agoraService.toggleMicrophone();
    setState(() {
      _isMuted = _agoraService.muted;
    });
  }

  Future<void> _toggleSpeaker() async {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
    });
    await _agoraService.engine.setEnableSpeakerphone(_isSpeakerOn);
  }

  // This method is now primarily for when the *local user* initiates the hangup,
  // or when Agora events indicate a non-recoverable call termination from this device's perspective.
  // The actual screen dismissal is handled by _handleCallScreenDismiss via the Firestore listener.
  void _handleCallEnd({bool isLocalUserLeaving = false}) async {
    print('📞 [AUDIO_CALL_SCREEN] Handling call end logic. Local user leaving: $isLocalUserLeaving. Call ID: ${widget.callId}');
    
    // If this device is initiating the hangup, tell CallService.
    if (isLocalUserLeaving && !_isScreenDismissed) { // also check _isScreenDismissed to avoid double actions
      try {
        print('📞 [AUDIO_CALL_SCREEN] Local user ending call, calling _callService.endCall for ${widget.callId}');
        await _callService.endCall(widget.callId);
        // The Firestore listener will then pick up the 'ended' status and trigger _handleCallScreenDismiss.
      } catch (e) {
        print('📞 [AUDIO_CALL_SCREEN] Error calling _callService.endCall for ${widget.callId}: $e');
        // If CallService.endCall fails, we might still want to dismiss the screen locally.
        // However, the Firestore listener should ideally handle this.
        // For robustness, if an error occurs here, we could directly call _handleCallScreenDismiss
        // but it's better if Firestore is the source of truth for dismissal.
         _handleCallScreenDismiss(); // Fallback dismissal if endCall fails catastrophically
      }
    } else if (!isLocalUserLeaving && !_isScreenDismissed) {
      // This case means an Agora event (like onUserOffline, onError, onConnectionLost) triggered _handleCallEnd.
      // We don't call _callService.endCall() here because the other user might have ended it,
      // or it's a network issue. The Firestore listener should reflect the true state.
      // We can, however, ensure local resources are cleaned up.
      print('📞 [AUDIO_CALL_SCREEN] Call end triggered by Agora event (not local user). Call ID: ${widget.callId}');
      _callTimer?.cancel();
      _agoraService.leaveChannel(); // Ensure Agora channel is left
      // _handleCallScreenDismiss will be called by the Firestore listener.
    }
    // If _isScreenDismissed is true, _handleCallScreenDismiss has already run or is running.
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // When user tries to swipe back, treat it as them ending the call.
        print('📞 [AUDIO_CALL_SCREEN] WillPopScope triggered for call ${widget.callId}.');
        _handleCallEnd(isLocalUserLeaving: true);
        // We return false because _handleCallScreenDismiss will handle the actual pop/navigation.
        // If we return true, it might pop before _handleCallScreenDismiss completes its async work or Firestore updates.
        return false;
      },
      child: Scaffold(
        backgroundColor: AppColors.splashColor,
        body: SafeArea(
          child: Column(
            children: [
              // Call status
              Expanded(
                flex: 2,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Avatar
                      CircleAvatar(
                        radius: 80,
                        backgroundColor: Colors.grey[700],
                        backgroundImage: widget.callerAvatar != null
                            ? NetworkImage(widget.callerAvatar!)
                            : null,
                        child: widget.callerAvatar == null
                            ? const Icon(
                                Icons.person,
                                size: 80,
                                color: Colors.white,
                              )
                            : null,
                      ),
                      const SizedBox(height: 20),
                      
                      // Caller name
                      Text(
                        widget.callerName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 10),
                      
                      // Call status
                      Text(
                        _getCallStatusText(),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
              
              // Call controls
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(20),
                  child: widget.isIncoming && !_isCallAnswered
                      ? _buildIncomingCallControls()
                      : _buildCallControls(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getCallStatusText() {
    if (widget.isIncoming && !_isCallAnswered) {
      return 'Incoming call...';
    } else if (!_isCallConnected) {
      return 'Connecting...';
    } else if (!_isCallAnswered) {
      return 'Calling...';
    } else {
      return _formatCallDuration(_callDuration);
    }
  }

  Widget _buildIncomingCallControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Decline button
        Container(
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () => _handleCallEnd(isLocalUserLeaving: true),
            icon: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
        
        // Answer button
        Container(
          decoration: const BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: _answerCall,
            icon: const Icon(
              Icons.call,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
      ],
    );
  }

  Widget _buildCallControls() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Call control buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Mute button
            Container(
              decoration: BoxDecoration(
                color: _isMuted ? Colors.red : Colors.grey[700],
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _toggleMute,
                icon: Icon(
                  _isMuted ? Icons.mic_off : Icons.mic,
                  color: Colors.white,
                ),
                iconSize: 50,
              ),
            ),
            
            // Speaker button
            Container(
              decoration: BoxDecoration(
                color: _isSpeakerOn ? AppColors.appColor : Colors.grey[700],
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: _toggleSpeaker,
                icon: Icon(
                  _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                  color: Colors.white,
                ),
                iconSize: 50,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 40),
        
        // End call button
        Container(
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: () => _handleCallEnd(isLocalUserLeaving: true),
            icon: const Icon(
              Icons.call_end,
              color: Colors.white,
              size: 30,
            ),
            iconSize: 60,
          ),
        ),
      ],
    );
  }
}