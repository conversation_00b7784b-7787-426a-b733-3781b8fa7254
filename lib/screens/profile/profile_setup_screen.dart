import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tolk/models/language_model.dart';
import 'package:tolk/screens/chat/chat_list_screen.dart';
import 'package:tolk/screens/profile/language_selection_screen.dart';
import 'package:tolk/services/language_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/utilities.dart';

class ProfileSetupScreen extends StatefulWidget {
  const ProfileSetupScreen({super.key});

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final TextEditingController _nameController = TextEditingController();
  String _selectedLanguageCode = 'en';
  Language? _selectedLanguage;
  File? _profileImage;
  bool _isLoading = false;
  List<Language> _allLanguages = [];
  bool _languagesLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadLanguages();
  }

  Future<void> _loadLanguages() async {
    try {
      final languages = await LanguageService.getTranslationLanguages();
      final defaultLanguage = languages.firstWhere(
        (lang) => lang.code == 'en',
        orElse: () => languages.first,
      );

      if (mounted) {
        setState(() {
          _allLanguages = languages;
          _selectedLanguage = defaultLanguage;
          _selectedLanguageCode = defaultLanguage.code;
          _languagesLoaded = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _languagesLoaded = true;
        });
      }
    }
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _profileImage = File(pickedFile.path);
      });
    }
  }

  Future<String?> _uploadProfileImage(String uid) async {
    if (_profileImage == null) return null;
    final ref = FirebaseStorage.instance
        .ref()
        .child('profile_pictures')
        .child('$uid.jpg');
    await ref.putFile(_profileImage!);
    return await ref.getDownloadURL();
  }

  Future<void> _submitProfile() async {
    if (_nameController.text.trim().isEmpty || _selectedLanguage == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please fill all fields and select a translation language.',
          ),
        ),
      );
      return;
    }
    setState(() {
      _isLoading = true;
    });
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) throw Exception('User not logged in');
      final imageUrl = await _uploadProfileImage(user.uid);
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'phoneNumber': user.phoneNumber,
        'name': _nameController.text.trim(),
        'profilePicture': imageUrl,
        'translationLanguage': _selectedLanguageCode,
        'createdAt': FieldValue.serverTimestamp(),
      });
      if (mounted) {
        pushAndRemoveScreen(context, const ChatListScreen());
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to save profile: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      appBar: AppBar(
        title: const Text(
          'Setup Profile',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.appColor,
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: _pickImage,
                child: CircleAvatar(
                  radius: 48,
                  backgroundImage:
                      _profileImage != null ? FileImage(_profileImage!) : null,
                  child:
                      _profileImage == null
                          ? const Icon(
                            Icons.camera_alt,
                            size: 40,
                            color: Colors.white,
                          )
                          : null,
                ),
              ),
              const SizedBox(height: 24),
              TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 24),

              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white54),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListTile(
                  title: const Text(
                    'Translation Language',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _languagesLoaded
                            ? (_selectedLanguage?.name ?? 'English')
                            : 'Loading...',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                      const Text(
                        'All chat messages will be translated to this language',
                        style: TextStyle(color: Colors.white54, fontSize: 12),
                      ),
                    ],
                  ),
                  trailing: const Icon(Icons.translate, color: Colors.white54),
                  onTap:
                      _languagesLoaded
                          ? () async {
                            await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => LanguageSelectionScreen(
                                      currentLanguageCode:
                                          _selectedLanguageCode,
                                      onLanguageSelected: (language) {
                                        if (mounted) {
                                          setState(() {
                                            _selectedLanguage = language;
                                            _selectedLanguageCode =
                                                language.code;
                                          });
                                        }
                                      },
                                    ),
                              ),
                            );
                          }
                          : null,
                ),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _submitProfile,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                  ),
                  child:
                      _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text(
                            'Save & Continue',
                            style: TextStyle(color: Colors.white),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
