import 'package:flutter/material.dart';
import 'package:tolk/models/language_model.dart';
import 'package:tolk/services/language_service.dart';
import 'package:tolk/utils/app_colors.dart';

class LanguageSelectionScreen extends StatefulWidget {
  final String currentLanguageCode;
  final Function(Language) onLanguageSelected;

  const LanguageSelectionScreen({
    super.key,
    required this.currentLanguageCode,
    required this.onLanguageSelected,
  });

  @override
  State<LanguageSelectionScreen> createState() =>
      _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  List<Language> _allLanguages = [];
  List<Language> _filteredLanguages = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLanguages();
    _searchController.addListener(_filterLanguages);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadLanguages() async {
    try {
      final allLanguages = await LanguageService.getTranslationLanguages();

      setState(() {
        _allLanguages = allLanguages;
        _filteredLanguages = allLanguages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterLanguages() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredLanguages = _allLanguages;
      } else {
        _filteredLanguages =
            _allLanguages
                .where(
                  (language) =>
                      language.name.toLowerCase().contains(query) ||
                      language.code.toLowerCase().contains(query),
                )
                .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      appBar: AppBar(
        title: const Text(
          'Translation Language',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.appColor,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'Search languages...',
                hintStyle: const TextStyle(color: Colors.white70),
                prefixIcon: const Icon(Icons.search, color: Colors.white70),
                filled: true,
                fillColor: Colors.grey[800],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Language List
          Expanded(
            child:
                _isLoading
                    ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.appColor,
                      ),
                    )
                    : _filteredLanguages.isEmpty
                    ? const Center(
                      child: Text(
                        'No languages found',
                        style: TextStyle(color: Colors.white70),
                      ),
                    )
                    : ListView.builder(
                      itemCount: _filteredLanguages.length,
                      itemBuilder: (context, index) {
                        final language = _filteredLanguages[index];
                        final isSelected =
                            language.code == widget.currentLanguageCode;

                        return ListTile(
                          title: Text(
                            language.name,
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? AppColors.appColor
                                      : Colors.white,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                          subtitle: Text(
                            language.code.toUpperCase(),
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? AppColors.appColor.withValues(
                                        alpha: 0.7,
                                      )
                                      : Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                          trailing:
                              isSelected
                                  ? const Icon(
                                    Icons.check,
                                    color: AppColors.appColor,
                                  )
                                  : null,
                          onTap: () {
                            widget.onLanguageSelected(language);
                            Navigator.pop(context);
                          },
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }
}
