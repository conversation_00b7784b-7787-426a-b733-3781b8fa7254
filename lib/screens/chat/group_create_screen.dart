import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/contact_provider.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/screens/chat/chat_screen.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/models/chat_models.dart';

class GroupCreateScreen extends StatefulWidget {
  const GroupCreateScreen({Key? key}) : super(key: key);

  @override
  State<GroupCreateScreen> createState() => _GroupCreateScreenState();
}

class _GroupCreateScreenState extends State<GroupCreateScreen> {
  final TextEditingController _groupNameController = TextEditingController();
  final Set<String> _selectedUserIds = {};
  bool _isLoading = false;

  @override
  void dispose() {
    _groupNameController.dispose();
    super.dispose();
  }

  Future<void> _onCreateGroup() async {
    final groupName = _groupNameController.text.trim();
    if (groupName.isEmpty || _selectedUserIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Enter group name and select at least one contact'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final chatService = ChatService();
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final currentUser = userProvider.currentUser;
      if (currentUser == null) throw Exception('User not found');

      // Add current user to group
      final participants = [currentUser.uid, ..._selectedUserIds];

      // Create group chat room using public method
      final chatRoomId = await chatService.createGroupChatRoom(
        groupName: groupName,
        participantIds: participants,
        groupImage: null,
      );

      // Send system message: "(my user name) created this group"
      final systemMsg = '${currentUser.name ?? "Someone"} created this group';
      await chatService.sendMessage(
        chatRoomId: chatRoomId,
        text: systemMsg,
        type: MessageType.text,
        metadata: {'isSystemMessage': true},
      );

      if (!mounted) return;
      Navigator.of(context).pop();
      Navigator.of(context).push(MaterialPageRoute(
        builder: (_) => ChatScreen(chatRoomId: chatRoomId, otherUser: null),
      ));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create group: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final contactProvider = Provider.of<ContactProvider>(context);
    final userProvider = Provider.of<UserProvider>(context);
    final currentUser = userProvider.currentUser;

    // Registered device contacts
    final registeredContacts = contactProvider.savedContacts
        .where((c) => c.isRegistered && c.userId != null && c.userId != currentUser?.uid)
        .toList();

    // Users already in chats (contactedUsers)
    final chatUsers = contactProvider.contactedUsers
        .where((u) => u.uid != currentUser?.uid)
        .map((u) => ContactModel(
              id: '',
              displayName: u.name ?? u.phoneNumber ?? 'Unknown',
              phoneNumbers: [u.phoneNumber ?? ''],
              userId: u.uid,
              isRegistered: true,
              profilePicture: u.profilePicture,
            ))
        .toList();

    // Merge and deduplicate by userId
    final allContactsMap = <String, ContactModel>{};
    for (final c in [...registeredContacts, ...chatUsers]) {
      if (c.userId != null && c.userId!.isNotEmpty) {
        allContactsMap[c.userId!] = c;
      }
    }
    final allContacts = allContactsMap.values.toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Group'),
        backgroundColor: AppColors.appColor,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Group Name',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _groupNameController,
                    decoration: const InputDecoration(
                      hintText: 'Enter group name',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    'Add Members',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: allContacts.isEmpty
                        ? const Text('No contacts found.')
                        : ListView.builder(
                            itemCount: allContacts.length,
                            itemBuilder: (context, index) {
                              final contact = allContacts[index];
                              final userId = contact.userId ?? '';
                              // Debug log for profile picture and contact info
                              // ignore: avoid_print
                              print('[GroupCreateScreen] Contact: name=${contact.displayName}, userId=$userId, profilePicture=${contact.profilePicture}, phoneNumbers=${contact.phoneNumbers}');
                              return CheckboxListTile(
                                value: userId.isNotEmpty && _selectedUserIds.contains(userId),
                                onChanged: (selected) {
                                  setState(() {
                                    if (selected == true && userId.isNotEmpty) {
                                      _selectedUserIds.add(userId);
                                    } else {
                                      _selectedUserIds.remove(userId);
                                    }
                                  });
                                },
                                title: Text(
                                  contact.displayName.isNotEmpty
                                      ? contact.displayName
                                      : contact.phoneNumbers.isNotEmpty
                                          ? contact.phoneNumbers.first
                                          : 'Unknown',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: contact.phoneNumbers.isNotEmpty
                                    ? Text(contact.phoneNumbers.first)
                                    : null,
                                secondary: (contact.profilePicture != null && contact.profilePicture!.isNotEmpty)
                                    ? CircleAvatar(
                                        backgroundImage: NetworkImage(contact.profilePicture!),
                                        radius: 20,
                                      )
                                    : CircleAvatar(
                                        child: Icon(Icons.person, color: Colors.white),
                                        radius: 20,
                                        backgroundColor: Colors.grey,
                                      ),
                              );
                            },
                          ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.group_add),
                      label: const Text('Create Group'),
                      onPressed: _onCreateGroup,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.appColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}