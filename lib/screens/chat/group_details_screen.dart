import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/screens/chat/chat_screen.dart'; // Import ChatScreen
import 'package:tolk/screens/chat/user_search_screen.dart'; // Import UserSearchScreen

class GroupDetailsScreen extends StatefulWidget {
  final String chatRoomId;

  const GroupDetailsScreen({super.key, required this.chatRoomId});

  @override
  State<GroupDetailsScreen> createState() => _GroupDetailsScreenState();
}

class _GroupDetailsScreenState extends State<GroupDetailsScreen> {
  final ChatService _chatService = ChatService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Group Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: _addMember, // Implement this method
            tooltip: 'Add Member',
          ),
        ],
      ),
      body: FutureBuilder<ChatRoom?>(
        future: _chatService.getChatRoomById(widget.chatRoomId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError || !snapshot.hasData || snapshot.data == null) {
            return const Center(child: Text('Error loading group details'));
          }

          final chatRoom = snapshot.data!;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey,
                  backgroundImage:
                      chatRoom.groupImage != null
                          ? CachedNetworkImageProvider(chatRoom.groupImage!)
                          : null,
                  child:
                      chatRoom.groupImage == null
                          ? const Icon(
                            Icons.group,
                            size: 50,
                            color: Colors.white,
                          )
                          : null,
                ),
                const SizedBox(height: 10),
                Text(
                  chatRoom.groupName ?? 'Group Chat',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'Members',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                // Display member list
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: chatRoom.participants.length,
                  itemBuilder: (context, index) {
                    final memberUid = chatRoom.participants[index];
                    return FutureBuilder<UserModel?>(
                      future: Provider.of<UserProvider>(
                        context,
                        listen: false,
                      ).getUserById(memberUid),
                      builder: (context, userSnapshot) {
                        if (userSnapshot.connectionState ==
                            ConnectionState.waiting) {
                          return const ListTile(
                            leading: CircleAvatar(backgroundColor: Colors.grey),
                            title: Text('Loading...'),
                          );
                        }
                        if (userSnapshot.hasError ||
                            !userSnapshot.hasData ||
                            userSnapshot.data == null) {
                          return const ListTile(
                            leading: CircleAvatar(backgroundColor: Colors.red),
                            title: Text('Error loading user'),
                          );
                        }
                        final member = userSnapshot.data!;
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 4,
                          ),
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8.0,
                              horizontal: 16.0,
                            ),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: Colors.grey,
                                  backgroundImage:
                                      member.profilePicture != null
                                          ? CachedNetworkImageProvider(
                                            member.profilePicture!,
                                          )
                                          : null,
                                  child:
                                      member.profilePicture == null
                                          ? const Icon(
                                            Icons.person,
                                            color: Colors.white,
                                          )
                                          : null,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        member.name ??
                                            member.phoneNumber ??
                                            'Unknown',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        member.phoneNumber ?? 'N/A',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.message),
                                  onPressed: () async {
                                    final currentUser =
                                        Provider.of<UserProvider>(
                                          context,
                                          listen: false,
                                        ).currentUser;
                                    if (currentUser == null) return;

                                    // Get or create chat room for individual chat
                                    final chatRoomId = await _chatService
                                        .createOrGetChatRoom(member.uid);

                                    if (chatRoomId.isNotEmpty) {
                                      // Navigate to individual chat screen
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) => ChatScreen(
                                                chatRoomId: chatRoomId,
                                                otherUser:
                                                    member, // Pass the member's UserModel
                                              ),
                                        ),
                                      );
                                    } else {
                                      // Handle error if chat room creation/retrieval fails
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text('Unable to start chat'),
                                        ),
                                      );
                                    }
                                  },
                                ),
                                // Remove member button (only if current user is creator and not removing self)
                                if (chatRoom.createdBy ==
                                        Provider.of<UserProvider>(
                                          context,
                                          listen: false,
                                        ).currentUser?.uid &&
                                    member.uid !=
                                        Provider.of<UserProvider>(
                                          context,
                                          listen: false,
                                        ).currentUser?.uid)
                                  IconButton(
                                    icon: const Icon(
                                      Icons.remove_circle_outline,
                                      color: Colors.red,
                                    ),
                                    onPressed: () async {
                                      // Show confirmation dialog
                                      final confirm = await showDialog<bool>(
                                        context: context,
                                        builder:
                                            (context) => AlertDialog(
                                              title: const Text(
                                                'Remove Member?',
                                              ),
                                              content: Text(
                                                'Are you sure you want to remove ${member.name ?? member.phoneNumber} from the group?',
                                              ),
                                              actions: [
                                                TextButton(
                                                  onPressed:
                                                      () => Navigator.pop(
                                                        context,
                                                        false,
                                                      ),
                                                  child: const Text('Cancel'),
                                                ),
                                                TextButton(
                                                  onPressed:
                                                      () => Navigator.pop(
                                                        context,
                                                        true,
                                                      ),
                                                  child: const Text(
                                                    'Remove',
                                                    style: TextStyle(
                                                      color: Colors.red,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                      );

                                      if (confirm == true) {
                                        try {
                                          await _chatService
                                              .removeParticipantFromGroup(
                                                widget.chatRoomId,
                                                member.uid,
                                              );
                                          // Refresh the screen to show updated member list
                                          setState(() {});
                                        } catch (e) {
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                'Failed to remove member: $e',
                                              ),
                                            ),
                                          );
                                        }
                                      }
                                    },
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
                const SizedBox(
                  height: 20,
                ), // Add spacing before the leave group option
                ElevatedButton.icon(
                  onPressed: _leaveGroup,
                  icon: const Icon(Icons.exit_to_app),
                  label: const Text('Leave Group'),
                  style: ElevatedButton.styleFrom(
                    fixedSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(
                  height: 20,
                ), // Add spacing before the add member, label: label)
              ],
            ),
          );
        },
      ),
    );
  }

  void _addMember() async {
    // Fetch the latest chat room data to get current participants
    final chatRoom = await _chatService.getChatRoomById(widget.chatRoomId);
    if (chatRoom == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error fetching group details')),
      );
      return;
    }

    // Navigate to UserSearchScreen to select new members
    final selectedMemberUids = await Navigator.push<List<String>>(
      context,
      MaterialPageRoute(
        builder:
            (context) => UserSearchScreen(
              existingMemberUids:
                  chatRoom.participants, // Pass existing members
            ),
      ),
    );

    // If users were selected, add them to the group
    if (selectedMemberUids != null && selectedMemberUids.isNotEmpty) {
      try {
        await _chatService.addParticipantsToGroup(
          widget.chatRoomId,
          selectedMemberUids,
        );
        // Refresh the screen to show newly added members
        setState(() {});
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to add members: $e')));
      }
    }
  }

  void _leaveGroup() async {
    final currentUser =
        Provider.of<UserProvider>(context, listen: false).currentUser;
    if (currentUser == null) return;

    // Show confirmation dialog
    final confirm = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Leave Group?'),
            content: const Text('Are you sure you want to leave this group?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Leave', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );

    if (confirm == true) {
      try {
        await _chatService.removeParticipantFromGroup(
          widget.chatRoomId,
          currentUser.uid,
        );
        // Navigate back to chat list after leaving
        Navigator.popUntil(context, (route) => route.isFirst);
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to leave group: $e')));
      }
    }
  }
}
