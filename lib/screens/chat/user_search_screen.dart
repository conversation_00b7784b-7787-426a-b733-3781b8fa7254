import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/providers/contact_provider.dart';
import 'package:tolk/screens/chat/chat_screen.dart';
import 'package:tolk/services/chat_service.dart';
import 'package:tolk/services/contact_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/utilities.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:developer' as developer;

class UserSearchScreen extends StatefulWidget {
  final List<String>? existingMemberUids; // Add this parameter

  const UserSearchScreen({super.key, this.existingMemberUids});

  @override
  State<UserSearchScreen> createState() => _UserSearchScreenState();
}

// Combined search result item
class SearchResultItem {
  final String id;
  final String displayName;
  final String? phoneNumber;
  final String? profilePicture;
  final bool isOnline;
  final bool isRegisteredUser;
  final bool hasExistingChat;
  final UserModel? userModel;
  final ContactModel? contactModel;

  SearchResultItem({
    required this.id,
    required this.displayName,
    this.phoneNumber,
    this.profilePicture,
    this.isOnline = false,
    required this.isRegisteredUser,
    required this.hasExistingChat,
    this.userModel,
    this.contactModel,
  });
}

class _UserSearchScreenState extends State<UserSearchScreen> {
  final ChatService _chatService = ChatService();
  final ContactService _contactService = ContactService();
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;
  final List<SearchResultItem> _searchResults = [];
  final List<UserModel> _contactedUsers = [];
  final List<ContactModel> _savedContacts = [];
  final Set<String> _selectedUserUids = {}; // Track selected users

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    developer.log(
      '🔍 UserSearchScreen: Starting to load data from cache...',
      name: 'UserSearchScreen',
    );

    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final currentUser = userProvider.currentUser;
    if (currentUser == null) {
      developer.log(
        '❌ UserSearchScreen: No current user found',
        name: 'UserSearchScreen',
      );
      return;
    }

    developer.log(
      '👤 UserSearchScreen: Current user: ${currentUser.name ?? currentUser.phoneNumber}',
      name: 'UserSearchScreen',
    );

    setState(() {
      _isLoading = true;
    });

    try {
      final contactProvider = Provider.of<ContactProvider>(
        context,
        listen: false,
      );

      // Check if contacts are already loaded in background
      if (contactProvider.hasLoadedOnce) {
        developer.log(
          '⚡ UserSearchScreen: Using cached contacts from background loading',
          name: 'UserSearchScreen',
        );

        // Use cached data
        setState(() {
          _contactedUsers.clear();
          _contactedUsers.addAll(contactProvider.contactedUsers);
          _savedContacts.clear();
          _savedContacts.addAll(contactProvider.savedContacts);
          _isLoading = false;
        });

        _updateSearchResults();
        developer.log(
          '✅ UserSearchScreen: Instant loading completed using cache',
          name: 'UserSearchScreen',
        );
        return;
      }

      // If not loaded yet, trigger loading and wait
      developer.log(
        '⏳ UserSearchScreen: Cache not ready, triggering background load...',
        name: 'UserSearchScreen',
      );

      await contactProvider.loadContactsInBackground();

      // Use the loaded data
      setState(() {
        _contactedUsers.clear();
        _contactedUsers.addAll(contactProvider.contactedUsers);
        _savedContacts.clear();
        _savedContacts.addAll(contactProvider.savedContacts);
        _isLoading = false;
      });

      _updateSearchResults();
      developer.log(
        '✅ UserSearchScreen: Data loading completed successfully',
        name: 'UserSearchScreen',
      );
    } catch (e) {
      developer.log(
        '❌ UserSearchScreen: Error in _loadData: $e',
        name: 'UserSearchScreen',
      );
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _updateSearchResults() {
    final currentUser =
        Provider.of<UserProvider>(context, listen: false).currentUser;
    if (currentUser == null) return;

    final registeredUsers = <SearchResultItem>[];
    final nonRegisteredContacts = <SearchResultItem>[];
    final query = _searchQuery.toLowerCase();

    // Add contacted users (always registered)
    for (final user in _contactedUsers) {
      // Exclude existing group members
      if (widget.existingMemberUids != null && widget.existingMemberUids!.contains(user.uid)) {
        continue;
      }

      // Check if this user is saved in contacts with a custom name
      final savedContact = _savedContacts.firstWhere(
        (contact) => contact.phoneNumbers.contains(user.phoneNumber),
        orElse: () => ContactModel(id: '', displayName: '', phoneNumbers: []),
      );

      // Use saved contact name if available, otherwise use user's registered name
      final displayName =
          savedContact.displayName.isNotEmpty
              ? savedContact.displayName
              : (user.name ?? user.phoneNumber);

      if (query.isEmpty ||
          displayName.toLowerCase().contains(query) ||
          (user.name?.toLowerCase().contains(query) ?? false) ||
          user.phoneNumber.toLowerCase().contains(query)) {
        registeredUsers.add(
          SearchResultItem(
            id: user.uid,
            displayName: displayName,
            phoneNumber: user.phoneNumber,
            profilePicture: user.profilePicture,
            isOnline: user.isOnline,
            isRegisteredUser: true,
            hasExistingChat: true,
            userModel: user,
          ),
        );
      }
    }

    // Add saved contacts and separate by registration status
    for (final contact in _savedContacts) {
      if (query.isEmpty ||
          contact.displayName.toLowerCase().contains(query) ||
          contact.phoneNumbers.any(
            (phone) => phone.toLowerCase().contains(query),
          )) {
        // Skip if this contact is already in contacted users (to avoid duplicates)
        final isAlreadyContacted = _contactedUsers.any(
          (user) => contact.phoneNumbers.contains(user.phoneNumber),
        );

        // Exclude existing group members (if contact is a registered user)
        if (contact.isRegistered && contact.userId != null && widget.existingMemberUids != null && widget.existingMemberUids!.contains(contact.userId!)) {
           continue;
        }


        if (!isAlreadyContacted) {
          // For registered contacts, create a UserModel with the saved contact name
          UserModel? userModel;
          if (contact.isRegistered && contact.userId != null) {
            final registeredUser = _contactedUsers.firstWhere(
              (user) => user.uid == contact.userId,
              orElse:
                  () => UserModel(
                    uid: contact.userId!,
                    phoneNumber: contact.primaryPhoneNumber ?? '',
                    name: contact.displayName, // Use saved contact name
                    createdAt: DateTime.now(),
                  ),
            );

            // Create a copy with the saved contact name
            userModel = registeredUser.copyWith(name: contact.displayName);
          }

          final searchItem = SearchResultItem(
            id: contact.id,
            displayName: contact.displayName, // Always use saved contact name
            phoneNumber: contact.primaryPhoneNumber,
            profilePicture: contact.profilePicture,
            isRegisteredUser: contact.isRegistered,
            hasExistingChat: false,
            contactModel: contact,
            userModel: userModel,
          );

          // Separate registered and non-registered contacts
          if (contact.isRegistered) {
            registeredUsers.add(searchItem);
          } else {
            nonRegisteredContacts.add(searchItem);
          }
        }
      }
    }

    // Sort both lists alphabetically
    registeredUsers.sort(
      (a, b) =>
          a.displayName.toLowerCase().compareTo(b.displayName.toLowerCase()),
    );
    nonRegisteredContacts.sort(
      (a, b) =>
          a.displayName.toLowerCase().compareTo(b.displayName.toLowerCase()),
    );

    // Combine lists: registered users first, then non-registered contacts
    final results = <SearchResultItem>[];
    results.addAll(registeredUsers);
    results.addAll(nonRegisteredContacts);

    setState(() {
      _searchResults.clear();
      _searchResults.addAll(results);
    });
  }

  @override
  Widget build(BuildContext context) {
    final isAddingMembers = widget.existingMemberUids != null; // Determine if in "add members" mode

    return Scaffold(
      appBar: AppBar(
        title: isAddingMembers
            ? const Text('Add Members') // Title for adding members
            : TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Search users...',
                  hintStyle: TextStyle(color: Colors.white70),
                  border: InputBorder.none,
                ),
                style: const TextStyle(color: Colors.white),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.trim();
                  });
                  _updateSearchResults();
                },
              ),
        backgroundColor: AppColors.appColor,
        actions: [
          if (!isAddingMembers && _searchQuery.isNotEmpty) // Show clear button only in search mode
            IconButton(
              icon: const Icon(Icons.clear, color: Colors.white),
              onPressed: () {
                _searchController.clear();
                setState(() {
                  _searchQuery = '';
                });
                _updateSearchResults();
              },
            ),
           if (isAddingMembers && _selectedUserUids.isNotEmpty) // Show done button in add members mode
             IconButton(
               icon: const Icon(Icons.done, color: Colors.white),
               onPressed: () {
                 Navigator.pop(context, _selectedUserUids.toList()); // Return selected UIDs
               },
             ),
        ],
      ),
      backgroundColor: AppColors.splashColor,
      body: Consumer<ContactProvider>(
        builder: (context, contactProvider, child) {
          // Update local state when provider data changes
          if (contactProvider.hasLoadedOnce &&
              (_contactedUsers.length !=
                      contactProvider.contactedUsers.length ||
                  _savedContacts.length !=
                      contactProvider.savedContacts.length)) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _contactedUsers.clear();
                _contactedUsers.addAll(contactProvider.contactedUsers);
                _savedContacts.clear();
                _savedContacts.addAll(contactProvider.savedContacts);
              });
              _updateSearchResults();
            });
          }

          return _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: AppColors.appColor),
              )
              : _searchResults.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _searchQuery.isEmpty ? Icons.contacts : Icons.search_off,
                      size: 80,
                      color: Colors.white54,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _searchQuery.isEmpty
                          ? 'Search your contacts and chats'
                          : 'No results found',
                      style: const TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_searchQuery.isEmpty) ...[
                      const SizedBox(height: 8),
                      const Text(
                        'Start typing to find contacts or people you\'ve chatted with',
                        style: TextStyle(color: Colors.white70),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              )
              : _buildSeparatedContactList(isAddingMembers); // Pass mode to list builder
        },
      ),
    );
  }

  Widget _buildSeparatedContactList(bool isAddingMembers) {
    // Separate registered users and non-registered contacts
    final registeredUsers =
        _searchResults.where((result) => result.isRegisteredUser).toList();
    final nonRegisteredContacts =
        _searchResults.where((result) => !result.isRegisteredUser).toList();

    return ListView.builder(
      itemCount: _calculateTotalItems(registeredUsers, nonRegisteredContacts),
      itemBuilder: (context, index) {
        return _buildListItem(index, registeredUsers, nonRegisteredContacts, isAddingMembers); // Pass mode to list item builder
      },
    );
  }

  int _calculateTotalItems(
    List<SearchResultItem> registeredUsers,
    List<SearchResultItem> nonRegisteredContacts,
  ) {
    int count = 0;

    // Add registered users section
    if (registeredUsers.isNotEmpty) {
      count += 1; // Header
      count += registeredUsers.length; // Users
    }

    // Add non-registered contacts section
    if (nonRegisteredContacts.isNotEmpty) {
      count += 1; // Header
      count += nonRegisteredContacts.length; // Contacts
    }

    // Add import contacts button if no permission and no device contacts
    if (_shouldShowImportButton()) {
      count += 1; // Import button
    }

    return count;
  }

  bool _shouldShowImportButton() {
    // Show import button if:
    // 1. No device contacts (indicating no permission or no contacts)
    // 2. Only showing contacted users (no device contacts loaded)
    // 3. Search query is empty (don't show during search)
    return _searchQuery.isEmpty &&
        _savedContacts.isEmpty &&
        _contactedUsers.isNotEmpty;
  }

  Widget _buildListItem(
    int index,
    List<SearchResultItem> registeredUsers,
    List<SearchResultItem> nonRegisteredContacts,
    bool isAddingMembers, // Receive mode
  ) {
    int currentIndex = 0;

    // Registered users section
    if (registeredUsers.isNotEmpty) {
      if (index == currentIndex) {
        // Registered users header
        return _buildSectionHeader('On Tolk', registeredUsers.length);
      }
      currentIndex++;

      if (index < currentIndex + registeredUsers.length) {
        // Registered user item
        final userIndex = index - currentIndex;
        return _buildSearchResultTile(registeredUsers[userIndex], isAddingMembers); // Pass mode to tile builder
      }
      currentIndex += registeredUsers.length;
    }

    // Non-registered contacts section
    if (nonRegisteredContacts.isNotEmpty) {
      if (index == currentIndex) {
        // Non-registered contacts header
        return _buildSectionHeader(
          'Invite to Tolk',
          nonRegisteredContacts.length,
        );
      }
      currentIndex++;

      if (index < currentIndex + nonRegisteredContacts.length) {
        // Non-registered contact item
        final contactIndex = index - currentIndex;
        return _buildSearchResultTile(nonRegisteredContacts[contactIndex], isAddingMembers); // Pass mode to tile builder
      }
      currentIndex += nonRegisteredContacts.length;
    }

    // Import contacts button
    if (_shouldShowImportButton() && index == currentIndex) {
      return _buildImportContactsButton();
    }

    // Fallback (should not happen)
    return const SizedBox.shrink();
  }

  Widget _buildImportContactsButton() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Divider
          const Divider(color: Colors.white12, height: 32),

          // Import button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton.icon(
              onPressed: _importContacts,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.appColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              icon: const Icon(Icons.contacts, size: 24),
              label: const Text(
                'Import Contacts',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Description text
          Text(
            'Find friends who are already using Tolk by importing your contacts',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count) {
    final contactProvider = Provider.of<ContactProvider>(context);
    final isOnTolkSection = title == 'On Tolk';
    final showLoading =
        isOnTolkSection && contactProvider.isLoadingRegistrationStatus;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: AppColors.splashColor.withValues(alpha: 0.3),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          if (showLoading) ...[
            const SizedBox(
              width: 12,
              height: 12,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white70),
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              'Loading...',
              style: TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ] else ...[
            Text(
              '($count)',
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSearchResultTile(SearchResultItem result, bool isAddingMembers) {
    // Only show registered users in "add members" mode
    if (isAddingMembers && !result.isRegisteredUser) {
      return const SizedBox.shrink();
    }

    return ListTile(
      leading: Stack(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: Colors.grey,
            backgroundImage:
                result.profilePicture != null
                    ? CachedNetworkImageProvider(result.profilePicture!)
                    : null,
            child:
                result.profilePicture == null
                    ? const Icon(Icons.person, color: Colors.white)
                    : null,
          ),
          if (result.isOnline)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppColors.splashColor, width: 2),
                ),
              ),
            ),
        ],
      ),
      title: Text(
        result.displayName,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (result.phoneNumber != null)
            Text(
              result.phoneNumber!,
              style: const TextStyle(color: Colors.white70, fontSize: 12),
            ),
          Text(
            result.hasExistingChat
                ? 'Recent chat'
                : result.isRegisteredUser
                ? 'On Tolk'
                : 'Not on Tolk',
            style: TextStyle(
              color:
                  result.hasExistingChat
                      ? AppColors.appColor
                      : result.isRegisteredUser
                      ? Colors.green
                      : Colors.orange,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      trailing: isAddingMembers // Show checkbox in "add members" mode
          ? Checkbox(
              value: _selectedUserUids.contains(result.userModel?.uid),
              onChanged: (bool? selected) {
                setState(() {
                  if (selected != null && result.userModel?.uid != null) {
                    if (selected) {
                      _selectedUserUids.add(result.userModel!.uid);
                    } else {
                      _selectedUserUids.remove(result.userModel!.uid);
                    }
                  }
                });
              },
            )
          : result.isRegisteredUser // Show Message/Invite button in search mode
               ? ElevatedButton(
                 onPressed: () => _openChat(result),
                 style: ElevatedButton.styleFrom(
                   backgroundColor: AppColors.appColor,
                   foregroundColor: Colors.white,
                   padding: const EdgeInsets.symmetric(
                     horizontal: 16,
                     vertical: 8,
                   ),
                   minimumSize: const Size(80, 32),
                 ),
                 child: const Text('Message', style: TextStyle(fontSize: 12)),
               )
               : OutlinedButton(
                 onPressed: () => _inviteContact(result),
                 style: OutlinedButton.styleFrom(
                   foregroundColor: Colors.white,
                   side: const BorderSide(color: Colors.white70),
                   padding: const EdgeInsets.symmetric(
                     horizontal: 16,
                     vertical: 8,
                   ),
                   minimumSize: const Size(80, 32),
                 ),
                 child: const Text('Invite', style: TextStyle(fontSize: 12)),
               ),
    );
  }

  Future<void> _openChat(SearchResultItem result) async {
    if (!result.isRegisteredUser || result.userModel == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final chatRoomId = await _chatService.createOrGetChatRoom(
        result.userModel!.uid,
      );
      if (mounted) {
        pushScreen(
          context,
          ChatScreen(chatRoomId: chatRoomId, otherUser: result.userModel!),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _importContacts() async {
    developer.log(
      '📱 UserSearchScreen: Import contacts button clicked',
      name: 'UserSearchScreen',
    );

    // Get provider reference before any async operations
    final contactProvider = Provider.of<ContactProvider>(
      context,
      listen: false,
    );

    try {
      // Check if permission is already granted
      final hasPermission = await _contactService.hasContactsPermission();

      if (!hasPermission) {
        developer.log(
          '🔐 UserSearchScreen: Requesting contacts permission...',
          name: 'UserSearchScreen',
        );

        // Request permission
        final granted = await _contactService.requestContactsPermission();

        if (!granted) {
          developer.log(
            '❌ UserSearchScreen: Contacts permission denied',
            name: 'UserSearchScreen',
          );

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Contacts permission is needed to import your contacts',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
          return;
        }

        developer.log(
          '✅ UserSearchScreen: Contacts permission granted',
          name: 'UserSearchScreen',
        );
      }

      // Show loading state
      setState(() {
        _isLoading = true;
      });

      // Update permission status and refresh contacts in provider
      await contactProvider.updateContactsPermission();

      // Refresh contacts to get latest data
      await contactProvider.refreshContacts();

      // Update local state with refreshed data
      if (mounted) {
        setState(() {
          _contactedUsers.clear();
          _contactedUsers.addAll(contactProvider.contactedUsers);
          _savedContacts.clear();
          _savedContacts.addAll(contactProvider.savedContacts);
        });

        _updateSearchResults();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Contacts imported successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      developer.log(
        '❌ UserSearchScreen: Error importing contacts: $e',
        name: 'UserSearchScreen',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error importing contacts: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _inviteContact(SearchResultItem result) async {
    developer.log(
      '📱 UserSearchScreen: Inviting contact ${result.displayName}',
      name: 'UserSearchScreen',
    );

    if (result.phoneNumber == null) {
      developer.log(
        '❌ UserSearchScreen: No phone number for contact ${result.displayName}',
        name: 'UserSearchScreen',
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No phone number available for this contact'),
          ),
        );
      }
      return;
    }

    // Clean phone number (remove spaces, dashes, etc. but keep +)
    final phoneNumber = result.phoneNumber!.replaceAll(RegExp(r'[^\d+]'), '');
    final message =
        'Hey! I\'m using Tolk for messaging. Download it here: https://play.google.com/store/apps/details?id=com.tolk.app';

    developer.log(
      '📞 UserSearchScreen: Opening SMS app for $phoneNumber',
      name: 'UserSearchScreen',
    );

    try {
      // Create SMS URI with phone number and pre-filled message
      final uri = Uri.parse(
        'sms:$phoneNumber?body=${Uri.encodeComponent(message)}',
      );

      developer.log(
        '🔗 UserSearchScreen: SMS URI: $uri',
        name: 'UserSearchScreen',
      );

      // Check if SMS app can be launched
      if (await canLaunchUrl(uri)) {
        developer.log(
          '✅ UserSearchScreen: Launching SMS app',
          name: 'UserSearchScreen',
        );
        await launchUrl(uri);
      } else {
        developer.log(
          '❌ UserSearchScreen: Cannot launch SMS app',
          name: 'UserSearchScreen',
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Could not open messaging app. Please check if SMS is available on your device.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      developer.log(
        '❌ UserSearchScreen: Error opening SMS app: $e',
        name: 'UserSearchScreen',
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening messaging app: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
