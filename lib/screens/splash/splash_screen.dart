import 'package:flutter/material.dart';
import 'package:tolk/screens/auth/login_screen.dart';
import 'package:tolk/screens/chat/chat_list_screen.dart';
import 'package:tolk/services/auth_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/app_strings.dart';
import 'package:tolk/utils/utilities.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    _controller.forward();

    Future.delayed(const Duration(milliseconds: 3000), () {
      if (mounted) {
        final authService = AuthService();
        if (authService.isLoggedIn) {
          pushAndRemoveScreen(context, const ChatListScreen());
        } else {
          pushAndRemoveScreen(context, const LoginScreen());
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: getDeviceHeightByPercent(context, 2.8),
                  ),
                  child: Image.asset(
                    'assets/images/splash.png',
                    height: getDeviceWidthByPercent(context, 68),
                    width: getDeviceWidthByPercent(context, 68),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
