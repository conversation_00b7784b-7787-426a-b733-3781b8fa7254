import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/screens/chat/chat_list_screen.dart';
import 'package:tolk/screens/profile/profile_screen.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/widgets/permission_check_widget.dart';
import 'package:tolk/services/permission_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  int _currentIndex = 0;
  final PermissionService _permissionService = PermissionService();
  bool _permissionsChecked = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _updateUserOnlineStatus(true);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _updateUserOnlineStatus(false);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _updateUserOnlineStatus(true);
    } else {
      _updateUserOnlineStatus(false);
    }
  }

  void _updateUserOnlineStatus(bool isOnline) {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    userProvider.updateOnlineStatus(isOnline);
  }

  void _onPermissionsGranted() {
    setState(() {
      _permissionsChecked = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);

    if (userProvider.isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final currentUser = userProvider.currentUser;
    if (currentUser == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    final List<Widget> screens = [
      const ChatListScreen(),
      ProfileScreen(user: currentUser),
    ];

    final homeContent = Scaffold(
      body: screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        backgroundColor: Colors.grey[900],
        selectedItemColor: AppColors.appColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.chat), label: 'Chats'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );

    // Wrap the home content with permission check
    return PermissionCheckWidget(
      onPermissionsGranted: _onPermissionsGranted,
      child: homeContent,
    );
  }
}
