import 'package:country_code_picker/country_code_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:tolk/screens/auth/otp_verification_screen.dart';
import 'package:tolk/services/auth_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/app_strings.dart';
import 'package:tolk/utils/utilities.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final FirebaseAuth _authService = FirebaseAuth.instance;
  String _countryCode = '+1';
  bool _isLoading = false;

  String _getFriendlyErrorMessage(String errorCode, String? originalMessage) {
    switch (errorCode) {
      case 'invalid-phone-number':
        return 'Please enter a valid phone number';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      case 'app-not-authorized':
        return 'App not authorized for phone verification';
      case 'captcha-check-failed':
        return 'Verification failed. Please try again';
      case 'web-context-cancelled':
        return 'Verification cancelled. Please try again';
      case 'web-context-canceled':
        return 'Verification cancelled. Please try again';
      case 'missing-phone-number':
        return 'Please enter your phone number';
      case 'invalid-verification-code':
        return 'Invalid verification code';
      case 'session-expired':
        return 'Session expired. Please try again';
      default:
        // For unknown errors, show a generic message
        return 'Unable to send verification code. Please try again';
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _onCountryChange(CountryCode countryCode) {
    setState(() {
      _countryCode = countryCode.dialCode ?? '+1';
    });
  }

  Future<void> _verifyPhoneNumber() async {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter your phone number')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final phoneNumber = '$_countryCode${_phoneController.text.trim()}';

    try {
      await _authService.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          await _authService.signInWithCredential(credential);
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
            // Navigate to home screen or next screen
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          setState(() {
            _isLoading = false;
          });
          final friendlyMessage = _getFriendlyErrorMessage(e.code, e.message);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                friendlyMessage,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: AppColors.appColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          setState(() {
            _isLoading = false;
          });
          // Navigate to OTP verification screen
          pushScreen(
            context,
            OTPVerificationScreen(
              verificationId: verificationId,
              phoneNumber: phoneNumber,
            ),
          );
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
        },
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        String errorMessage =
            'Unable to send verification code. Please try again';
        if (e is FirebaseAuthException) {
          errorMessage = _getFriendlyErrorMessage(e.code, e.message);
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              errorMessage,
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: getDeviceHeightByPercent(context, 10)),
              Center(
                child: Image.asset(
                  'assets/images/splash.png',
                  height: getDeviceWidthByPercent(context, 40),
                  width: getDeviceWidthByPercent(context, 40),
                ),
              ),
              SizedBox(height: getDeviceHeightByPercent(context, 5)),
              Text(
                AppStrings.loginWithPhone,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                AppStrings.phoneVerificationMessage,
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
              SizedBox(height: getDeviceHeightByPercent(context, 5)),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    CountryCodePicker(
                      dialogBackgroundColor: AppColors.dialogColor,
                      onChanged: _onCountryChange,
                      initialSelection: 'US',
                      favorite: const ['+1', '+92'],
                      showCountryOnly: false,
                      showOnlyCountryWhenClosed: false,
                      alignLeft: false,
                    ),
                    Expanded(
                      child: TextField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        decoration: const InputDecoration(
                          hintText: AppStrings.phoneNumber,
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 10),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _verifyPhoneNumber,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text(
                            AppStrings.continueText,
                            style: TextStyle(fontSize: 16),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
