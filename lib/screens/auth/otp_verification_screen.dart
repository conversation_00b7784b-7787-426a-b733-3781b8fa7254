import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:tolk/screens/chat/chat_list_screen.dart';
import 'package:tolk/services/auth_service.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/utils/app_strings.dart';
import 'package:tolk/utils/utilities.dart';

import '../profile/profile_setup_screen.dart';

class OTPVerificationScreen extends StatefulWidget {
  final String verificationId;
  final String phoneNumber;

  const OTPVerificationScreen({
    super.key,
    required this.verificationId,
    required this.phoneNumber,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  final TextEditingController _otpController = TextEditingController();
  final AuthService _authService = AuthService();
  bool _isLoading = false;
  int _remainingTime = 60;
  Timer? _timer;
  bool _canResend = false;

  String _getFriendlyErrorMessage(String errorCode, String? originalMessage) {
    switch (errorCode) {
      case 'invalid-verification-code':
        return 'Invalid verification code. Please check and try again';
      case 'session-expired':
        return 'Verification code expired. Please request a new one';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later';
      case 'network-request-failed':
        return 'Network error. Please check your connection';
      case 'invalid-verification-id':
        return 'Invalid verification session. Please restart the process';
      case 'missing-verification-code':
        return 'Please enter the verification code';
      case 'credential-already-in-use':
        return 'This phone number is already registered';
      case 'operation-not-allowed':
        return 'Phone authentication is not enabled';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later';
      case 'app-not-authorized':
        return 'App not authorized for phone verification';
      case 'captcha-check-failed':
        return 'Verification failed. Please try again';
      case 'web-context-cancelled':
        return 'Verification cancelled. Please try again';
      case 'web-context-canceled':
        return 'Verification cancelled. Please try again';
      default:
        // For unknown errors, show a generic message
        return 'Verification failed. Please try again';
    }
  }

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        setState(() {
          _remainingTime--;
        });
      } else {
        setState(() {
          _canResend = true;
        });
        _timer?.cancel();
      }
    });
  }

  String get _formattedTime {
    final minutes = (_remainingTime ~/ 60).toString().padLeft(2, '0');
    final seconds = (_remainingTime % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  Future<void> _verifyOTP() async {
    if (_otpController.text.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid 6-digit code')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final credential = await _authService.verifyOTP(
        verificationId: widget.verificationId,
        smsCode: _otpController.text.trim(),
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (credential.user != null) {
          // Check Firestore for user profile
          final userDoc =
              await FirebaseFirestore.instance
                  .collection('users')
                  .doc(credential.user!.uid)
                  .get();

          if (mounted) {
            if (userDoc.exists &&
                userDoc.data() != null &&
                userDoc.data()!.containsKey('name')) {
              pushAndRemoveScreen(context, const ChatListScreen());
            } else {
              pushAndRemoveScreen(context, const ProfileSetupScreen());
            }
          }
        }
      }
    } on FirebaseAuthException catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        final friendlyMessage = _getFriendlyErrorMessage(e.code, e.message);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              friendlyMessage,
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Verification failed. Please try again',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  Future<void> _resendOTP() async {
    if (!_canResend) return;

    setState(() {
      _isLoading = true;
      _canResend = false;
      _remainingTime = 60;
    });

    try {
      await _authService.verifyPhoneNumber(
        phoneNumber: widget.phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          await _authService.signInWithCredential(credential);
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
            pushAndRemoveScreen(context, const ChatListScreen());
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          setState(() {
            _isLoading = false;
          });
          final friendlyMessage = _getFriendlyErrorMessage(e.code, e.message);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                friendlyMessage,
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: AppColors.appColor,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          setState(() {
            _isLoading = false;
          });
          // Update verification ID
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('OTP sent successfully')),
            );
          }
          _startTimer();
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          // Auto-retrieval timeout
        },
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        String errorMessage = 'Unable to resend code. Please try again';
        if (e is FirebaseAuthException) {
          errorMessage = _getFriendlyErrorMessage(e.code, e.message);
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              errorMessage,
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: getDeviceHeightByPercent(context, 5)),
              Text(
                AppStrings.verificationCode,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${AppStrings.verificationSentTo} ${widget.phoneNumber}',
                style: const TextStyle(fontSize: 16, color: Colors.white),
              ),
              SizedBox(height: getDeviceHeightByPercent(context, 5)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: PinCodeTextField(
                  appContext: context,
                  length: 6,
                  controller: _otpController,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(8),
                    fieldHeight: 50,
                    fieldWidth: 45,
                    activeFillColor: AppColors.otpFieldColor,
                    inactiveFillColor: AppColors.otpFieldColor,
                    selectedFillColor: AppColors.otpFieldColor,
                    activeColor: AppColors.appColor,
                    inactiveColor: Colors.grey.shade300,
                    selectedColor: AppColors.appColor,
                  ),
                  keyboardType: TextInputType.number,
                  enableActiveFill: true,
                  onChanged: (value) {},
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    AppStrings.didntReceiveCode,
                    style: TextStyle(fontSize: 14),
                  ),
                  _canResend
                      ? GestureDetector(
                        onTap: _resendOTP,
                        child: Text(
                          AppStrings.resend,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: AppColors.appColor,
                          ),
                        ),
                      )
                      : Text(
                        _formattedTime,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.appColor,
                        ),
                      ),
                ],
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _verifyOTP,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : const Text(
                            AppStrings.verify,
                            style: TextStyle(fontSize: 16),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
