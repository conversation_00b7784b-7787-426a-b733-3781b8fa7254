import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/screens/settings/account_screen.dart';
import 'package:tolk/widgets/permission_status_widget.dart';
import 'package:tolk/screens/settings/webview_screen.dart';
import 'package:tolk/services/auth_service.dart'; // Import AuthService
import 'package:tolk/screens/auth/login_screen.dart'; // Import LoginScreen

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final currentUser = Provider.of<UserProvider>(context).currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body:
          currentUser == null
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                children: [
                  // User Info Section (Card Style)
                  _buildSettingsCard(context, [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 40,
                            backgroundColor: Colors.grey,
                            backgroundImage:
                                currentUser.profilePicture != null
                                    ? CachedNetworkImageProvider(
                                      currentUser.profilePicture!,
                                    )
                                    : null,
                            child:
                                currentUser.profilePicture == null
                                    ? const Icon(
                                      Icons.person,
                                      color: Colors.white,
                                      size: 40,
                                    )
                                    : null,
                          ),
                          const SizedBox(width: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currentUser.name ?? 'User',
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                currentUser.phoneNumber ?? 'N/A',
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ]),

                  // Settings Options (Card Style)
                  _buildSettingsCard(context, [
                    _buildSettingsTile(
                      icon: Icons.account_circle,
                      title: 'Account',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AccountScreen(),
                          ),
                        );
                      },
                    ),
                    _buildSettingsTile(
                      icon: Icons.security,
                      title: 'App Permissions',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const PermissionStatusWidget(),
                          ),
                        );
                      },
                    ),
                    _buildSettingsTile(
                      icon: Icons.privacy_tip,
                      title: 'Privacy',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const WebViewScreen(
                                  title: 'Privacy Policy',
                                  url: 'https://www.google.com/', // Dummy URL
                                ),
                          ),
                        );
                      },
                    ),
                    _buildSettingsTile(
                      icon: Icons.help,
                      title: 'Help & Support',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const WebViewScreen(
                                  title: 'Help & Support',
                                  url:
                                      'https://www.example.com/help', // Dummy URL
                                ),
                          ),
                        );
                      },
                    ),
                    _buildSettingsTile(
                      icon: Icons.info,
                      title: 'About',
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => const WebViewScreen(
                                  title: 'About Us',
                                  url:
                                      'https://www.example.com/about', // Dummy URL
                                ),
                          ),
                        );
                      },
                    ),
                  ]),

                  // Logout Option (Separate Card)
                  _buildSettingsCard(context, [
                    _buildSettingsTile(
                      icon: Icons.logout,
                      title: 'Logout',
                      textColor: Colors.red,
                      iconColor: Colors.red,
                      onTap: () async {
                        // Implement Logout functionality
                        final authService = Provider.of<AuthService>(context, listen: false);
                        await authService.signOut();
                        // Navigate to login screen after logout
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(builder: (context) => const LoginScreen()),
                        );
                      },
                    ),
                  ]),
                ],
              ),
    );
  }

  Widget _buildSettingsCard(BuildContext context, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      elevation: 2.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      child: Column(children: children),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    VoidCallback? onTap,
    Color? textColor,
    Color? iconColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(title, style: TextStyle(color: textColor)),
      onTap: onTap,
    );
  }
}
