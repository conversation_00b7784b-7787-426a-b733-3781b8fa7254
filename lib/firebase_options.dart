// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCgCmOS9OrOObseAcHazE9JbZNHF7lLwHM',
    appId: '1:393289530782:android:4696f39ab13ab80a332a8f',
    messagingSenderId: '393289530782',
    projectId: 'multilingual-chat-app-85bd5',
    databaseURL: 'https://multilingual-chat-app-85bd5.firebaseio.com',
    storageBucket: 'multilingual-chat-app-85bd5.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB7_jffY5pzxLX0PxDXlqvZfyG76UQ5fR4',
    appId: '1:393289530782:ios:b0561dbb285bf7ad332a8f',
    messagingSenderId: '393289530782',
    projectId: 'multilingual-chat-app-85bd5',
    databaseURL: 'https://multilingual-chat-app-85bd5.firebaseio.com',
    storageBucket: 'multilingual-chat-app-85bd5.appspot.com',
    androidClientId: '393289530782-d6ib0ps4jc8ep6pj323eigi9qbek01oi.apps.googleusercontent.com',
    iosClientId: '393289530782-i5fd6ec8fh5sqgfr1ag1cjodc8rudgkt.apps.googleusercontent.com',
    iosBundleId: 'com.multilinguall.app.tolk',
  );

}