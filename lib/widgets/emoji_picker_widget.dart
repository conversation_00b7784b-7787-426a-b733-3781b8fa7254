import 'package:flutter/material.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import '../utils/app_colors.dart';

class EmojiPickerWidget extends StatelessWidget {
  final Function(String) onEmojiSelected;
  final VoidCallback onBackspacePressed;

  const EmojiPickerWidget({
    super.key,
    required this.onEmojiSelected,
    required this.onBackspacePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250,
      decoration: const BoxDecoration(
        color: Color(0xFF242225),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white54,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Emoji picker
          Expanded(
            child: EmojiPicker(
              onEmojiSelected: (category, emoji) {
                onEmojiSelected(emoji.emoji);
              },
              onBackspacePressed: onBackspacePressed,
              config: Config(
                height: 200,
                checkPlatformCompatibility: true,
                emojiViewConfig: EmojiViewConfig(
                  backgroundColor: const Color(0xFF242225),
                  columns: 7,
                  emojiSizeMax: 28,
                  verticalSpacing: 0,
                  horizontalSpacing: 0,
                  gridPadding: EdgeInsets.zero,
                  recentsLimit: 28,
                  replaceEmojiOnLimitExceed: false,
                  noRecents: const Text(
                    'No Recents',
                    style: TextStyle(fontSize: 20, color: Colors.white54),
                    textAlign: TextAlign.center,
                  ),
                  loadingIndicator: const SizedBox.shrink(),
                  buttonMode: ButtonMode.MATERIAL,
                ),
                skinToneConfig: const SkinToneConfig(),
                categoryViewConfig: CategoryViewConfig(
                  backgroundColor: const Color(0xFF242225),
                  indicatorColor: AppColors.appColor,
                  iconColor: Colors.grey,
                  iconColorSelected: AppColors.appColor,
                  backspaceColor: AppColors.appColor,
                  categoryIcons: const CategoryIcons(),
                ),
                bottomActionBarConfig: const BottomActionBarConfig(
                  backgroundColor: Color(0xFF242225),
                  enabled: false,
                ),
                searchViewConfig: SearchViewConfig(
                  backgroundColor: const Color(0xFF242225),
                  buttonIconColor: AppColors.appColor,
                  hintText: 'Search emoji',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Custom emoji categories widget for better theming
class CustomEmojiCategories extends StatelessWidget {
  final Category selectedCategory;
  final Function(Category) onCategorySelected;

  const CustomEmojiCategories({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final categories = [
      Category.RECENT,
      Category.SMILEYS,
      Category.ANIMALS,
      Category.FOODS,
      Category.TRAVEL,
      Category.ACTIVITIES,
      Category.OBJECTS,
      Category.SYMBOLS,
      Category.FLAGS,
    ];

    return Container(
      height: 50,
      decoration: const BoxDecoration(
        color: Color(0xFF242225),
        border: Border(top: BorderSide(color: Colors.white12, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children:
            categories.map((category) {
              final isSelected = category == selectedCategory;
              return GestureDetector(
                onTap: () => onCategorySelected(category),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? AppColors.appColor.withValues(alpha: 0.2)
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getCategoryIcon(category),
                    color: isSelected ? AppColors.appColor : Colors.grey,
                    size: 20,
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  IconData _getCategoryIcon(Category category) {
    switch (category) {
      case Category.RECENT:
        return Icons.access_time;
      case Category.SMILEYS:
        return Icons.emoji_emotions;
      case Category.ANIMALS:
        return Icons.pets;
      case Category.FOODS:
        return Icons.fastfood;
      case Category.TRAVEL:
        return Icons.directions_car;
      case Category.ACTIVITIES:
        return Icons.sports_soccer;
      case Category.OBJECTS:
        return Icons.lightbulb;
      case Category.SYMBOLS:
        return Icons.favorite;
      case Category.FLAGS:
        return Icons.flag;
    }
  }
}

// Quick emoji reactions widget
class QuickEmojiReactions extends StatelessWidget {
  final Function(String) onEmojiSelected;

  const QuickEmojiReactions({super.key, required this.onEmojiSelected});

  @override
  Widget build(BuildContext context) {
    final quickEmojis = [
      '😀',
      '😂',
      '😍',
      '😢',
      '😮',
      '😡',
      '👍',
      '👎',
      '❤️',
      '🔥',
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: const BoxDecoration(
        color: Color(0xFF242225),
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children:
            quickEmojis.map((emoji) {
              return GestureDetector(
                onTap: () => onEmojiSelected(emoji),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(emoji, style: const TextStyle(fontSize: 20)),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }
}

// Emoji button for message input
class EmojiButton extends StatelessWidget {
  final bool isEmojiPickerVisible;
  final VoidCallback onPressed;

  const EmojiButton({
    super.key,
    required this.isEmojiPickerVisible,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(
        isEmojiPickerVisible ? Icons.keyboard : Icons.emoji_emotions,
        color: isEmojiPickerVisible ? AppColors.appColor : Colors.white70,
      ),
      onPressed: onPressed,
    );
  }
}
