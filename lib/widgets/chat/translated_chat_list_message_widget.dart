import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/services/translation_service.dart';

class TranslatedChatListMessageWidget extends StatefulWidget {
  final String? lastMessage;
  final String chatRoomId;
  final int unreadCount;

  const TranslatedChatListMessageWidget({
    super.key,
    required this.lastMessage,
    required this.chatRoomId,
    required this.unreadCount,
  });

  @override
  State<TranslatedChatListMessageWidget> createState() =>
      _TranslatedChatListMessageWidgetState();
}

class _TranslatedChatListMessageWidgetState
    extends State<TranslatedChatListMessageWidget>
    with AutomaticKeepAliveClientMixin {
  String? _translatedText;
  bool _isTranslating = false;

  // Static cache to store translations across widget rebuilds
  static final Map<String, Map<String, String>> _translationCache = {};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _checkAndTranslate();
  }

  @override
  void didUpdateWidget(TranslatedChatListMessageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // If the message changed, check for translation again
    if (oldWidget.lastMessage != widget.lastMessage) {
      _checkAndTranslate();
    }
  }

  Future<void> _checkAndTranslate() async {
    if (widget.lastMessage == null || widget.lastMessage!.isEmpty) {
      return;
    }

    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final currentUser = userProvider.currentUser;

    if (currentUser == null) return;

    final targetLanguage = currentUser.translationLanguage;
    final messageText = widget.lastMessage!;

    // Skip translation if target language is English and text appears to be English
    if (targetLanguage == 'en' &&
        TranslationService.isLikelyEnglish(messageText)) {
      return;
    }

    // Skip translation for media messages (they already have user-friendly text)
    if (_isMediaMessage(messageText)) {
      return;
    }

    // Check cache first
    final cacheKey = '${widget.chatRoomId}_$targetLanguage';
    if (_translationCache.containsKey(cacheKey) &&
        _translationCache[cacheKey]!.containsKey(messageText)) {
      final cachedTranslation = _translationCache[cacheKey]![messageText]!;
      if (mounted && cachedTranslation != messageText) {
        setState(() {
          _translatedText = cachedTranslation;
        });
      }
      return;
    }

    // Auto-translate to user's preferred language
    await _translateMessage(targetLanguage, messageText);
  }

  bool _isMediaMessage(String message) {
    // Check if message is a media message indicator
    return message.startsWith('📷') || // Photo
        message.startsWith('🎥') || // Video
        message.startsWith('🎵') || // Voice message
        message.startsWith('📄') || // File
        message.startsWith('📍') || // Location
        message == 'Voice message' ||
        message == 'Photo' ||
        message == 'Video' ||
        message == 'File' ||
        message == 'Location';
  }

  Future<void> _translateMessage(
    String targetLanguage,
    String messageText,
  ) async {
    if (_isTranslating) return;

    setState(() {
      _isTranslating = true;
    });

    try {
      final translatedText = await TranslationService.translateText(
        text: messageText,
        targetLanguage: targetLanguage,
      );

      if (mounted && translatedText != messageText) {
        // Cache the translation
        final cacheKey = '${widget.chatRoomId}_$targetLanguage';
        _translationCache[cacheKey] ??= {};
        _translationCache[cacheKey]![messageText] = translatedText;

        setState(() {
          _translatedText = translatedText;
        });
      }
    } catch (e) {
      debugPrint('Chat list translation error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isTranslating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    final displayText =
        _translatedText ?? widget.lastMessage ?? 'Start a conversation';

    return Text(
      displayText,
      style: TextStyle(
        color: widget.unreadCount > 0 ? Colors.white : Colors.white70,
        fontWeight:
            widget.unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}
