import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Import for date formatting
// import 'package:provider/provider.dart'; // No longer needed here
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/models/user_model.dart';
// import 'package:tolk/providers/user_provider.dart'; // No longer needed here
// import 'package:tolk/services/call_handler_service.dart'; // No longer needed here
import 'package:tolk/utils/app_colors.dart';

class CallMessageWidget extends StatelessWidget {
  final Message message;
  final bool isMe;
  final UserModel?
  chatPartnerUser; // Kept, as it might be useful for UI differentiation
  final Function(MessageType callType) onInitiateCallBack; // New callback

  const CallMessageWidget({
    super.key,
    required this.message,
    required this.isMe,
    required this.chatPartnerUser,
    required this.onInitiateCallBack, // New callback
  });

  @override
  Widget build(BuildContext context) {
    final bool isAudioCall = message.type == MessageType.audioCall;
    final IconData callIconData = isAudioCall ? Icons.call : Icons.videocam;
    // Use message.text for call status/type, assuming CallService populates it.
    // Example: "Missed audio call", "Video call - 01:23"
    final String callStatusText =
        message.text ?? (isAudioCall ? 'Audio Call' : 'Video Call');
    // final String buttonText = isAudioCall ? 'Call Back' : 'Join Call'; // Not directly used for label anymore

    // Determine colors based on sender/receiver to match other message bubbles
    final Color bubbleColor = isMe ? AppColors.appColor : Colors.grey[800]!;
    final Color textColor =
        Colors.white; // Text is white on both sender/receiver dark bubbles
    final Color primaryIconColor = Colors.white70; // Main call icon
    final Color buttonIconColor =
        isMe ? Colors.white : AppColors.appColor; // Icon on the button
    final Color buttonBackgroundColor =
        isMe ? Colors.white.withOpacity(0.2) : Colors.white.withOpacity(0.15);

    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        height: 80,
        // Constrain the width of the Card
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.5,
        ),
        child: InkWell(
          onTap: () {
            // Simply invoke the callback with the message's call type
            if (message.type == MessageType.audioCall) {
              onInitiateCallBack(MessageType.audioCall);
            } else if (message.type == MessageType.videoCall) {
              onInitiateCallBack(MessageType.videoCall);
            }
          },
          child: Card(
            color: bubbleColor,
            elevation: 1, // Keep elevation low
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ), // Consistent rounding
            margin: const EdgeInsets.symmetric(
              vertical: 4,
              horizontal: 8,
            ), // Standard margin
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 8.0,
              ), // Reduced padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Column 1: Call Icon
                      Icon(
                        callIconData,
                        color: primaryIconColor,
                        size: 24, // Reduced size
                      ),

                      // Column 2: Call Type/Status and Duration
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            callStatusText,
                            style: TextStyle(
                              color: textColor,
                              fontSize: 15, // Reduced size
                              fontWeight: FontWeight.w500, // Slightly less bold
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                              top: 1.0,
                            ), // Reduced top padding
                            child: Text(
                              message.metadata?['call_duration'] ??
                                  message.metadata?['call_status_detail'] ??
                                  'No Answer',
                              style: TextStyle(
                                color: textColor.withOpacity(0.8),
                                fontSize: 12, // Reduced size
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),

                  Text(
                    DateFormat('hh:mm a').format(message.timestamp),
                    style: TextStyle(
                      color: textColor.withOpacity(0.7),
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
