import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file/open_file.dart';
import 'package:just_audio/just_audio.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/utils/app_colors.dart';

class MediaMessageWidget extends StatefulWidget {
  final Message message;
  final bool isMe;

  const MediaMessageWidget({
    super.key,
    required this.message,
    required this.isMe,
  });

  @override
  State<MediaMessageWidget> createState() => _MediaMessageWidgetState();
}

class _MediaMessageWidgetState extends State<MediaMessageWidget> {
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;

  @override
  void initState() {
    super.initState();
    _audioPlayer.durationStream.listen((duration) {
      if (mounted) {
        setState(() {
          _duration = duration ?? Duration.zero;
        });
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if this is an encrypted media message that can't be decrypted
    final isEncryptedButCantDecrypt =
        widget.message.isEncrypted &&
        (widget.message.mediaUrl == null || widget.message.mediaUrl!.isEmpty) &&
        widget.message.encryptedMediaUrl != null;

    if (isEncryptedButCantDecrypt) {
      return _buildEncryptedMediaMessage();
    }

    switch (widget.message.type) {
      case MessageType.image:
        return _buildImageMessage(context);
      case MessageType.file:
        return _buildFileMessage(context);
      case MessageType.video:
        return _buildVideoMessage(context);
      case MessageType.audio:
        return _buildAudioMessage(context);
      case MessageType.location:
        return _buildLocationMessage(context);
      default:
        return _buildTextMessage();
    }
  }

  Widget _buildEncryptedMediaMessage() {
    IconData icon;
    String mediaType;

    switch (widget.message.type) {
      case MessageType.image:
        icon = Icons.image;
        mediaType = 'Image';
        break;
      case MessageType.video:
        icon = Icons.videocam;
        mediaType = 'Video';
        break;
      case MessageType.audio:
        icon = Icons.mic;
        mediaType = 'Voice message';
        break;
      case MessageType.file:
        icon = Icons.attach_file;
        mediaType = 'File';
        break;
      case MessageType.location:
        icon = Icons.location_on;
        mediaType = 'Location';
        break;
      default:
        icon = Icons.lock;
        mediaType = 'Media';
        break;
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 250),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white24),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white54, size: 24),
          const SizedBox(width: 12),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'This message is encrypted',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  mediaType,
                  style: const TextStyle(color: Colors.white54, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageMessage(BuildContext context) {
    final isUploading = widget.message.metadata?['isUploading'] == true;
    final localPath = widget.message.metadata?['localPath'] as String?;

    return GestureDetector(
      onTap: isUploading ? null : () => _showFullScreenImage(context),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 250),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                height: 200,
                width: double.infinity,
                color: Colors.grey[800],
                child:
                    isUploading
                        ? _buildUploadingImageCard(localPath)
                        : _buildLoadedImageContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadingImageCard(String? localPath) {
    // Show local image preview if available, otherwise show placeholder
    if (localPath != null) {
      return Image.file(
        File(localPath),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
      );
    } else {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[800],
        child: const Icon(Icons.image, color: Colors.white54, size: 40),
      );
    }
  }

  Widget _buildLoadedImageContent() {
    return widget.message.mediaUrl != null
        ? CachedNetworkImage(
          imageUrl: widget.message.mediaUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          placeholder:
              (context, url) => Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.grey[800],
                child: const Center(
                  child: CircularProgressIndicator(color: AppColors.appColor),
                ),
              ),
          errorWidget:
              (context, url, error) => Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.grey[800],
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, color: Colors.red, size: 40),
                    SizedBox(height: 8),
                    Text(
                      'Failed to load image',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
        )
        : Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.grey[800],
          child: const Center(
            child: Icon(Icons.image, color: Colors.white54, size: 40),
          ),
        );
  }

  Widget _buildFileMessage(BuildContext context) {
    final fileName = widget.message.metadata?['fileName'] ?? 'Unknown File';
    final fileSize = widget.message.metadata?['fileSize'] ?? 0;
    final fileExtension = widget.message.metadata?['fileExtension'] ?? '';

    return GestureDetector(
      onTap: () => _downloadAndOpenFile(),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.white24),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getFileIconColor(fileExtension),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getFileIcon(fileExtension),
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    fileName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    _formatFileSize(fileSize),
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            _isDownloading
                ? SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    value: _downloadProgress,
                    color: AppColors.appColor,
                  ),
                )
                : const Icon(Icons.download, color: Colors.white70, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoMessage(BuildContext context) {
    final isUploading = widget.message.metadata?['isUploading'] == true;
    final localPath = widget.message.metadata?['localPath'] as String?;

    return GestureDetector(
      onTap: isUploading ? null : () => _downloadAndOpenFile(),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 250),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                height: 200, // Same height as image preview
                width: double.infinity,
                color: Colors.grey[800],
                child:
                    isUploading
                        ? _buildUploadingVideoCard(localPath)
                        : _buildLoadedVideoContent(),
              ),
            ),
            const SizedBox(height: 8),
            // Video info row
            Row(
              children: [
                Icon(
                  Icons.videocam,
                  color: isUploading ? Colors.orange : Colors.white70,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  isUploading ? 'Uploading...' : 'Video',
                  style: TextStyle(
                    color: isUploading ? Colors.orange : Colors.white70,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatFileSize(widget.message.metadata?['fileSize'] ?? 0),
                  style: const TextStyle(color: Colors.white54, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUploadingVideoCard(String? localPath) {
    // Show video placeholder without loading overlay
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[800],
      child: const Icon(Icons.videocam, color: Colors.white54, size: 40),
    );
  }

  Widget _buildLoadedVideoContent() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Video thumbnail placeholder (could be enhanced with actual thumbnail)
        Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.grey[800],
          child: const Icon(Icons.videocam, color: Colors.white54, size: 40),
        ),
        // Play button overlay
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(30),
          ),
          child: const Icon(Icons.play_arrow, color: Colors.white, size: 36),
        ),
      ],
    );
  }

  Widget _buildAudioMessage(BuildContext context) {
    // Check if voice message is still uploading
    final isUploading = widget.message.metadata?['isUploading'] == true;
    final hasMediaUrl =
        widget.message.mediaUrl != null && widget.message.mediaUrl!.isNotEmpty;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800], // Neutral gray for both sent and received
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Play/Pause/Loading button
          GestureDetector(
            onTap: isUploading || !hasMediaUrl ? null : _toggleAudioPlayback,
            child: Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color:
                    isUploading
                        ? Colors.grey[600]
                        : Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(22),
                border: Border.all(
                  color:
                      isUploading
                          ? Colors.transparent
                          : Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child:
                  isUploading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                      : Icon(
                        _isPlaying ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 22,
                      ),
            ),
          ),
          const SizedBox(width: 16),

          // Audio content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Voice message header
                Row(
                  children: [
                    Icon(
                      Icons.graphic_eq,
                      color: isUploading ? Colors.orange : Colors.white70,
                      size: 18,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      isUploading ? 'Sending...' : 'Voice message',
                      style: TextStyle(
                        color: isUploading ? Colors.orange : Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Waveform visualization (simplified)
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 32,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: List.generate(20, (index) {
                            final heights = [
                              0.3,
                              0.7,
                              0.4,
                              0.9,
                              0.2,
                              0.8,
                              0.5,
                              0.6,
                              0.3,
                              0.7,
                              0.4,
                              0.8,
                              0.2,
                              0.9,
                              0.5,
                              0.6,
                              0.3,
                              0.7,
                              0.4,
                              0.8,
                            ];
                            final isActive =
                                !isUploading && _duration.inMilliseconds > 0
                                    ? index <
                                        (20 *
                                            _position.inMilliseconds /
                                            _duration.inMilliseconds)
                                    : false;

                            return Container(
                              width: 2,
                              height: 32 * heights[index],
                              decoration: BoxDecoration(
                                color:
                                    isUploading
                                        ? Colors.orange.withValues(alpha: 0.5)
                                        : isActive
                                        ? Colors.white
                                        : Colors.white.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(1),
                              ),
                            );
                          }),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      isUploading
                          ? 'Uploading...'
                          : _formatAudioDuration(_position, _duration),
                      style: TextStyle(
                        color: isUploading ? Colors.orange : Colors.white70,
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleAudioPlayback() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        if (widget.message.mediaUrl != null) {
          await _audioPlayer.setUrl(widget.message.mediaUrl!);
          await _audioPlayer.play();
        }
      }
    } catch (e) {
      // Remove error message - silent failure
    }
  }

  String _formatAudioDuration(Duration position, Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    if (duration.inMilliseconds > 0) {
      final positionStr =
          '${twoDigits(position.inMinutes)}:${twoDigits(position.inSeconds % 60)}';
      final durationStr =
          '${twoDigits(duration.inMinutes)}:${twoDigits(duration.inSeconds % 60)}';
      return '$positionStr / $durationStr';
    } else {
      return '${twoDigits(position.inMinutes)}:${twoDigits(position.inSeconds % 60)}';
    }
  }

  Widget _buildTextMessage() {
    return Text(
      widget.message.text ?? '',
      style: const TextStyle(color: Colors.white),
    );
  }

  void _showFullScreenImage(BuildContext context) {
    if (widget.message.mediaUrl == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              backgroundColor: Colors.black,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                iconTheme: const IconThemeData(color: Colors.white),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.download, color: Colors.white),
                    onPressed: () => _downloadImage(),
                  ),
                ],
              ),
              body: Center(
                child: InteractiveViewer(
                  child: CachedNetworkImage(
                    imageUrl: widget.message.mediaUrl!,
                    fit: BoxFit.contain,
                    placeholder:
                        (context, url) => const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.appColor,
                          ),
                        ),
                    errorWidget:
                        (context, url, error) => const Center(
                          child: Icon(Icons.error, color: Colors.red, size: 50),
                        ),
                  ),
                ),
              ),
            ),
      ),
    );
  }

  Future<void> _downloadImage() async {
    if (widget.message.mediaUrl == null) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final dio = Dio();
      final fileName =
          widget.message.metadata?['fileName'] ??
          'image_${DateTime.now().millisecondsSinceEpoch}.jpg';

      // Get appropriate download directory
      Directory directory;
      try {
        // Try to use Downloads directory on Android, Documents on iOS
        if (Platform.isAndroid) {
          directory = Directory('/storage/emulated/0/Download');
          if (!await directory.exists()) {
            // Fallback to app documents directory
            directory = await getApplicationDocumentsDirectory();
          }
        } else {
          // iOS - use app documents directory
          directory = await getApplicationDocumentsDirectory();
        }
      } catch (e) {
        // Fallback to app documents directory
        directory = await getApplicationDocumentsDirectory();
      }

      final filePath = '${directory.path}/$fileName';

      await dio.download(
        widget.message.mediaUrl!,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              _downloadProgress = received / total;
            });
          }
        },
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Saved successfully',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      // Remove error message - silent failure
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });
      }
    }
  }

  Future<void> _downloadAndOpenFile() async {
    if (widget.message.mediaUrl == null) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final dio = Dio();
      final fileName =
          widget.message.metadata?['fileName'] ??
          'file_${DateTime.now().millisecondsSinceEpoch}';

      // Get appropriate download directory
      Directory directory;
      try {
        // Try to use Downloads directory on Android, Documents on iOS
        if (Platform.isAndroid) {
          directory = Directory('/storage/emulated/0/Download');
          if (!await directory.exists()) {
            // Fallback to app documents directory
            directory = await getApplicationDocumentsDirectory();
          }
        } else {
          // iOS - use app documents directory
          directory = await getApplicationDocumentsDirectory();
        }
      } catch (e) {
        // Fallback to app documents directory
        directory = await getApplicationDocumentsDirectory();
      }

      final filePath = '${directory.path}/$fileName';

      await dio.download(
        widget.message.mediaUrl!,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() {
              _downloadProgress = received / total;
            });
          }
        },
      );

      // Try to open the file with multiple methods
      bool fileOpened = false;

      try {
        // Method 1: Try opening with file:// URI
        final fileUri = Uri.file(filePath);
        if (await canLaunchUrl(fileUri)) {
          await launchUrl(fileUri, mode: LaunchMode.externalApplication);
          fileOpened = true;
        }
      } catch (e) {
        // Method 1 failed, try other methods
      }

      if (!fileOpened) {
        try {
          // Method 2: Try using open_file package (most reliable)
          final result = await OpenFile.open(filePath);
          if (result.type == ResultType.done) {
            fileOpened = true;
          } else if (result.type == ResultType.noAppToOpen) {
            // Silent handling - no popup
            return;
          } else if (result.type == ResultType.fileNotFound) {
            // Silent handling - no popup
            return;
          } else if (result.type == ResultType.permissionDenied) {
            // Silent handling - no popup
            return;
          }
        } catch (e) {
          // Method 2 failed
        }
      }

      // Show download success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Saved successfully',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: AppColors.appColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } catch (e) {
      // Remove error message - silent failure
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });
      }
    }
  }

  Widget _buildLocationMessage(BuildContext context) {
    final latitude = widget.message.metadata?['latitude'] as double?;
    final longitude = widget.message.metadata?['longitude'] as double?;
    final address = widget.message.metadata?['address'] as String?;

    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white12, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Location header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.location_on,
                  color: Colors.red,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Location',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Address
          if (address != null && address.isNotEmpty)
            Text(
              address,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

          const SizedBox(height: 12),

          // Coordinates
          if (latitude != null && longitude != null)
            Text(
              '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}',
              style: const TextStyle(
                color: Colors.white54,
                fontSize: 12,
                fontFamily: 'monospace',
              ),
            ),

          const SizedBox(height: 12),

          // Open in maps button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed:
                  latitude != null && longitude != null
                      ? () => _openInMaps(latitude, longitude)
                      : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[700],
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.white24),
                ),
              ),
              icon: const Icon(Icons.map, size: 16),
              label: const Text('Open in Maps', style: TextStyle(fontSize: 14)),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openInMaps(double latitude, double longitude) async {
    try {
      final url =
          'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
      final uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
      // Remove error messages - silent failure
    } catch (e) {
      // Remove error messages - silent failure
    }
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'zip':
      case 'rar':
        return Icons.archive;
      case 'mp3':
      case 'wav':
        return Icons.audiotrack;
      case 'mp4':
      case 'avi':
        return Icons.videocam;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileIconColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Colors.red;
      case 'doc':
      case 'docx':
        return Colors.blue;
      case 'xls':
      case 'xlsx':
        return Colors.green;
      case 'ppt':
      case 'pptx':
        return Colors.orange;
      case 'zip':
      case 'rar':
        return Colors.purple;
      case 'mp3':
      case 'wav':
        return Colors.pink;
      case 'mp4':
      case 'avi':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
