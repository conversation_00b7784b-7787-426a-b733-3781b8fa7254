import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/services/contact_service.dart';
import 'package:tolk/utils/app_colors.dart';

class ContactImportDialog extends StatefulWidget {
  final VoidCallback? onImportComplete;
  final VoidCallback? onSkip;

  const ContactImportDialog({super.key, this.onImportComplete, this.onSkip});

  @override
  State<ContactImportDialog> createState() => _ContactImportDialogState();
}

class _ContactImportDialogState extends State<ContactImportDialog> {
  final ContactService _contactService = ContactService();
  bool _isImporting = false;
  String _statusMessage = '';
  List<ContactModel> _importedContacts = [];

  Future<void> _importContacts() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final currentUser = userProvider.currentUser;

    if (currentUser == null) return;

    setState(() {
      _isImporting = true;
      _statusMessage = 'Requesting permission...';
    });

    try {
      // Check/request permission
      final hasPermission = await _contactService.hasContactsPermission();
      if (!hasPermission) {
        setState(() {
          _statusMessage = 'Please grant contacts permission';
        });

        final granted = await _contactService.requestContactsPermission();
        if (!granted) {
          setState(() {
            _statusMessage = 'Permission denied';
            _isImporting = false;
          });
          return;
        }
      }

      setState(() {
        _statusMessage = 'Reading contacts...';
      });

      // Get device contacts and check registration (no saving)
      final deviceContacts = await _contactService.getDeviceContacts();
      final contacts = await _contactService.checkRegisteredContacts(
        deviceContacts,
      );

      setState(() {
        _importedContacts = contacts;
        _statusMessage = 'Found ${contacts.length} contacts';
        _isImporting = false;
      });

      // Wait a moment to show the result
      await Future.delayed(const Duration(seconds: 1));

      // Close dialog and notify completion
      if (mounted) {
        Navigator.of(context).pop();
        widget.onImportComplete?.call();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: ${e.toString()}';
        _isImporting = false;
      });
    }
  }

  void _skip() {
    Navigator.of(context).pop();
    widget.onSkip?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.splashColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.appColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.contacts,
                size: 40,
                color: AppColors.appColor,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            const Text(
              'Import Contacts',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Description
            Text(
              _isImporting
                  ? _statusMessage
                  : 'Find friends who are already using Tolk by importing your contacts. We\'ll help you connect with people you know.',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Loading indicator
            if (_isImporting) ...[
              const CircularProgressIndicator(color: AppColors.appColor),
              const SizedBox(height: 24),
            ],

            // Buttons
            if (!_isImporting) ...[
              // Import button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _importContacts,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.appColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Import Contacts',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // Skip button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: TextButton(
                  onPressed: _skip,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white70,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Skip for now',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],

            // Privacy note
            if (!_isImporting) ...[
              const SizedBox(height: 16),
              Text(
                'Your contacts are read from your device and not stored anywhere.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Helper function to show the contact import dialog
Future<void> showContactImportDialog(
  BuildContext context, {
  VoidCallback? onImportComplete,
  VoidCallback? onSkip,
}) {
  return showDialog(
    context: context,
    barrierDismissible: false, // Prevent dismissing by tapping outside
    builder:
        (context) => ContactImportDialog(
          onImportComplete: onImportComplete,
          onSkip: onSkip,
        ),
  );
}
