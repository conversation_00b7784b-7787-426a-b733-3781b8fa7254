import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/permission_service.dart';
import '../utils/app_colors.dart';

class PermissionStatusWidget extends StatefulWidget {
  const PermissionStatusWidget({super.key});

  @override
  State<PermissionStatusWidget> createState() => _PermissionStatusWidgetState();
}

class _PermissionStatusWidgetState extends State<PermissionStatusWidget> {
  final PermissionService _permissionService = PermissionService();
  Map<Permission, PermissionStatus> _permissionStatuses = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPermissionStatuses();
  }

  Future<void> _loadPermissionStatuses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final statuses = await _permissionService.getPermissionStatuses();
      setState(() {
        _permissionStatuses = statuses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _requestPermissions() async {
    final granted = await _permissionService.checkAndRequestPermissions(
      context,
    );
    if (granted) {
      await _loadPermissionStatuses();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All permissions granted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      await _loadPermissionStatuses();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.splashColor,
      appBar: AppBar(
        title: const Text(
          'App Permissions',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.appColor,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: AppColors.appColor),
              )
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Permission Status',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Manage app permissions to ensure all features work properly.',
                      style: TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                    const SizedBox(height: 24),

                    Expanded(
                      child: ListView(
                        children: [
                          _buildPermissionCard(
                            Icons.camera_alt,
                            'Camera',
                            'Take photos to share in chats',
                            Permission.camera,
                            Colors.blue,
                          ),
                          const SizedBox(height: 12),
                          _buildPermissionCard(
                            Icons.mic,
                            'Microphone',
                            'Record voice messages for sharing',
                            Permission.microphone,
                            Colors.red,
                          ),
                          const SizedBox(height: 12),
                          _buildPermissionCard(
                            Icons.photo_library,
                            'Photos & Media',
                            'Select images and videos from your device',
                            Permission.photos,
                            Colors.purple,
                          ),
                          const SizedBox(height: 12),
                          _buildPermissionCard(
                            Icons.notifications,
                            'Notifications',
                            'Receive message notifications',
                            Permission.notification,
                            Colors.green,
                          ),
                          const SizedBox(height: 12),
                          _buildPermissionCard(
                            Icons.contacts,
                            'Contacts',
                            'Find friends and import your contacts',
                            Permission.contacts,
                            Colors.orange,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton.icon(
                        onPressed: _requestPermissions,
                        icon: const Icon(Icons.security, color: Colors.white),
                        label: const Text(
                          'Request All Permissions',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.appColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: OutlinedButton.icon(
                        onPressed: () => openAppSettings(),
                        icon: const Icon(Icons.settings, color: Colors.white70),
                        label: const Text(
                          'Open App Settings',
                          style: TextStyle(color: Colors.white70, fontSize: 16),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Colors.white30),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildPermissionCard(
    IconData icon,
    String title,
    String description,
    Permission permission,
    Color iconColor,
  ) {
    final status = _permissionStatuses[permission];
    final isGranted = status?.isGranted ?? false;
    final isDenied = status?.isDenied ?? false;
    final isPermanentlyDenied = status?.isPermanentlyDenied ?? false;

    String statusText;
    Color statusColor;
    IconData statusIcon;

    if (isGranted) {
      statusText = 'Granted';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (isPermanentlyDenied) {
      statusText = 'Permanently Denied';
      statusColor = Colors.red;
      statusIcon = Icons.block;
    } else if (isDenied) {
      statusText = 'Denied';
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
    } else {
      statusText = 'Not Requested';
      statusColor = Colors.grey;
      statusIcon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isGranted
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: iconColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(color: Colors.white60, fontSize: 14),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Column(
            children: [
              Icon(statusIcon, color: statusColor, size: 24),
              const SizedBox(height: 4),
              Text(
                statusText,
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
