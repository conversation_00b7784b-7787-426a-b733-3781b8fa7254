import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/permission_service.dart';
import '../services/notification_service.dart';
import '../utils/app_colors.dart';

class PermissionCheckWidget extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPermissionsGranted;

  const PermissionCheckWidget({
    super.key,
    required this.child,
    this.onPermissionsGranted,
  });

  @override
  State<PermissionCheckWidget> createState() => _PermissionCheckWidgetState();
}

class _PermissionCheckWidgetState extends State<PermissionCheckWidget> {
  final PermissionService _permissionService = PermissionService();
  bool _isCheckingPermissions = true;
  bool _hasAllPermissions = false;
  Map<Permission, PermissionStatus> _permissionStatuses = {};
  late SharedPreferences prefs;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    prefs = await SharedPreferences.getInstance();

    if (prefs.getBool('hasSkippedPermissions') ?? false) {
      setState(() {
        _isCheckingPermissions = false;
        _hasAllPermissions = true;
      });
      // Initialize notification service for previously skipped permissions
      await _initializeNotifications();
      // Call the callback for previously skipped permissions
      widget.onPermissionsGranted?.call();
      return;
    }

    setState(() {
      _isCheckingPermissions = true;
    });

    try {
      final hasPermissions =
          await _permissionService.areAllPermissionsGranted();
      final statuses = await _permissionService.getPermissionStatuses();

      setState(() {
        _hasAllPermissions = hasPermissions;
        _permissionStatuses = statuses;
        _isCheckingPermissions = false;
      });

      if (hasPermissions) {
        // Initialize notification service when permissions are granted
        _initializeNotifications();
        widget.onPermissionsGranted?.call();
      }
    } catch (e) {
      setState(() {
        _isCheckingPermissions = false;
        _hasAllPermissions = false;
      });
    }
  }

  Future<void> _requestPermissions() async {
    final granted = await _permissionService.checkAndRequestPermissions(
      context,
    );

    if (granted) {
      setState(() {
        _hasAllPermissions = true;
      });
      // Initialize notification service when permissions are granted
      await _initializeNotifications();
      widget.onPermissionsGranted?.call();
    } else {
      // Refresh permission statuses
      await _checkPermissions();
    }
  }

  // Initialize notification service
  Future<void> _initializeNotifications() async {
    try {
      final notificationService = NotificationService();
      await notificationService.initialize();
      print('🔔 [PERMISSION] Notification service initialized');
    } catch (e) {
      print('🔔 [PERMISSION] Error initializing notifications: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isCheckingPermissions) {
      return Scaffold(
        backgroundColor: AppColors.splashColor,
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: AppColors.appColor),
              SizedBox(height: 16),
              Text(
                'Checking permissions...',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ),
      );
    }

    if (_hasAllPermissions) {
      return widget.child;
    }

    return Scaffold(
      backgroundColor: AppColors.splashColor,
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: IntrinsicHeight(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        const Spacer(),
                        // App Icon/Logo
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: AppColors.appColor,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(
                            Icons.chat,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Title
                        const Text(
                          'Welcome to Tolk',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 12),

                        // Subtitle
                        const Text(
                          'To provide you with the best experience, we need access to some features on your device.',
                          style: TextStyle(color: Colors.white70, fontSize: 16),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),

                        // Permission Status List
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.grey[900],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Column(
                            children: [
                              _buildPermissionStatus(
                                Icons.camera_alt,
                                'Camera',
                                'Take photos to share',
                                Permission.camera,
                              ),
                              const Divider(color: Colors.grey),
                              _buildPermissionStatus(
                                Icons.mic,
                                'Microphone',
                                'Record voice messages',
                                Permission.microphone,
                              ),
                              const Divider(color: Colors.grey),
                              _buildPermissionStatus(
                                Icons.photo_library,
                                'Photos & Media',
                                'Select images and videos',
                                Permission.photos,
                              ),
                              const Divider(color: Colors.grey),
                              _buildPermissionStatus(
                                Icons.notifications,
                                'Notifications',
                                'Receive message alerts',
                                Permission.notification,
                              ),
                              const Divider(color: Colors.grey),
                              _buildPermissionStatus(
                                Icons.contacts,
                                'Contacts',
                                'Find friends and import contacts',
                                Permission.contacts,
                              ),
                            ],
                          ),
                        ),

                        const Spacer(),

                        // Action Buttons
                        Column(
                          children: [
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: _requestPermissions,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.appColor,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Grant Permissions',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 12),
                            TextButton(
                              onPressed: () async {
                                prefs.setBool('hasSkippedPermissions', true);
                                setState(() {
                                  _hasAllPermissions = true;
                                });
                                // Initialize notification service even when skipped
                                await _initializeNotifications();
                                // Call the callback even when skipped so app can proceed
                                widget.onPermissionsGranted?.call();
                              },
                              child: const Text(
                                'Skip for now',
                                style: TextStyle(
                                  color: Colors.white60,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPermissionStatus(
    IconData icon,
    String title,
    String description,
    Permission permission,
  ) {
    final status = _permissionStatuses[permission];
    final isGranted = status?.isGranted ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isGranted ? Colors.green : Colors.grey[700],
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(color: Colors.white60, fontSize: 12),
                ),
              ],
            ),
          ),
          Icon(
            isGranted ? Icons.check_circle : Icons.circle_outlined,
            color: isGranted ? Colors.green : Colors.grey,
            size: 24,
          ),
        ],
      ),
    );
  }
}
