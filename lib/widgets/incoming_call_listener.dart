import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/providers/call_provider.dart';
import 'package:tolk/providers/user_provider.dart';
import 'package:tolk/services/call_service.dart';
// import 'package:tolk/services/token_service.dart'; // Not used directly here anymore
import 'package:tolk/services/notification_service.dart'; // Keep for potential future use or if screen needs it
// import 'package:tolk/screens/call/audio_call_screen.dart'; // Handled by ChatListScreen now
// import 'package:tolk/screens/call/video_call_screen.dart'; // Handled by ChatListScreen now

class IncomingCallListener extends StatefulWidget {
  final Widget child;

  const IncomingCallListener({
    super.key,
    required this.child,
  });

  @override
  State<IncomingCallListener> createState() => _IncomingCallListenerState();
}

class _IncomingCallListenerState extends State<IncomingCallListener> {
  final CallService _callService = CallService();
  // final NotificationService _notificationService = NotificationService(); // Not used directly here

  @override
  void initState() {
    super.initState();
    print('📞 [CALL_LISTENER] ========== INCOMING CALL LISTENER INIT ==========');
    print('📞 [CALL_LISTENER] Widget initialized');
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final currentUser = userProvider.currentUser;
    final callProvider = Provider.of<CallProvider>(context, listen: false);

    // print('📞 [CALL_LISTENER] ========== BUILD CALLED ==========');
    // print('📞 [CALL_LISTENER] UserProvider loading: ${userProvider.isLoading}');
    // print('📞 [CALL_LISTENER] Current user: ${currentUser?.uid}');
    // print('📞 [CALL_LISTENER] User name: ${currentUser?.name}');

    if (currentUser == null) {
      // print('📞 [CALL_LISTENER] ❌ No current user, skipping call listener');
      // If user logs out, ensure any existing call in provider is cleared.
      if (callProvider.incomingCall != null) {
        print('📞 [CALL_LISTENER] User logged out, clearing call from provider.');
        WidgetsBinding.instance.addPostFrameCallback((_) {
          callProvider.clearIncomingCall();
        });
      }
      return widget.child;
    }

    // print('📞 [CALL_LISTENER] ✅ Setting up call listener for user: ${currentUser.uid}');

    return StreamBuilder<CallData?>(
      stream: _callService.listenForIncomingCalls(currentUser.uid),
      builder: (context, snapshot) {
        // print('📞 [CALL_LISTENER] ========== STREAM UPDATE ==========');
        // print('📞 [CALL_LISTENER] Connection state: ${snapshot.connectionState}');
        // print('📞 [CALL_LISTENER] Has data: ${snapshot.hasData}');
        // print('📞 [CALL_LISTENER] Has error: ${snapshot.hasError}');
        // if (snapshot.hasError) {
        //   print('📞 [CALL_LISTENER] ❌ Stream error: ${snapshot.error}');
        // }
        // print('📞 [CALL_LISTENER] Data: ${snapshot.data?.callId}');

        if (snapshot.connectionState == ConnectionState.waiting && callProvider.incomingCall == null) {
          // Still waiting for initial data and no call is active in provider
          return widget.child;
        }

        if (snapshot.hasError) {
          print('📞 [CALL_LISTENER] ❌ Stream error: ${snapshot.error}, clearing call from provider.');
          WidgetsBinding.instance.addPostFrameCallback((_) {
             callProvider.clearIncomingCall();
          });
          return widget.child;
        }

        final callData = snapshot.data;

        if (callData != null && callData.status == 'calling') {
          // print('📞 [CALL_LISTENER] ✅ Incoming call detected: ${callData.callId} from ${callData.callerName}');
          // print('📞 [CALL_LISTENER] Call status: ${callData.status}');
          // Update provider only if it's a new call or status changed for the current call
          if (callProvider.incomingCall?.callId != callData.callId || callProvider.incomingCall?.status != callData.status) {
             print('📞 [CALL_LISTENER] Updating CallProvider with new call data: ${callData.callId}');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              callProvider.setIncomingCall(callData);
            });
          }
        } else {
          // No active 'calling' call, or call status is no longer 'calling'
          if (callProvider.incomingCall != null) {
            // If there was a call in the provider, clear it
            // This handles cases where the call is ended/declined/timed out on the other end
            // or if the stream returns null (e.g. call document deleted)
            print('📞 [CALL_LISTENER] No active call or status not "calling", clearing call from provider. Current provider call: ${callProvider.incomingCall?.callId}, status: ${callProvider.incomingCall?.status}. Stream data: ${callData?.callId}, status: ${callData?.status}');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              callProvider.clearIncomingCall();
            });
          }
        }
        
        return widget.child;
      },
    );
  }
}
// Removed IncomingCallScreen and _showIncomingCallDialog as it's no longer used here.
// The UI for incoming calls will be handled by ChatListScreen via CallProvider.