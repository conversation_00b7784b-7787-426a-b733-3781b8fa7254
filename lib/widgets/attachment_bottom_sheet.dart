import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:tolk/utils/app_colors.dart';
import 'package:tolk/services/permission_service.dart';

class AttachmentBottomSheet extends StatelessWidget {
  final Function(XFile) onImageSelected;
  final Function(PlatformFile) onFileSelected;
  final Function(XFile) onVideoSelected;
  final Function(Map<String, dynamic>) onLocationSelected;

  const AttachmentBottomSheet({
    super.key,
    required this.onImageSelected,
    required this.onFileSelected,
    required this.onVideoSelected,
    required this.onLocationSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppColors.splashColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white54,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Select Attachment',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAttachmentOption(
                context,
                icon: Icons.camera_alt,
                label: 'Camera',
                onTap: () => _pickImageFromCamera(context),
              ),
              _buildAttachmentOption(
                context,
                icon: Icons.photo_library,
                label: 'Gallery',
                onTap: () => _pickImageFromGallery(context),
              ),
              _buildAttachmentOption(
                context,
                icon: Icons.videocam,
                label: 'Video',
                onTap: () => _pickVideo(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildAttachmentOption(
                context,
                icon: Icons.insert_drive_file,
                label: 'Document',
                onTap: () => _pickFile(context),
              ),
              _buildAttachmentOption(
                context,
                icon: Icons.location_on,
                label: 'Location',
                onTap: () => _shareLocation(context),
              ),
              const SizedBox(width: 60), // Empty space for alignment
            ],
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.appColor,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(icon, color: Colors.white, size: 30),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImageFromCamera(BuildContext context) async {
    Navigator.pop(context);

    // Check camera permission using PermissionService
    final permissionService = PermissionService();
    final hasPermission = await permissionService.hasCameraPermission();

    if (!hasPermission) {
      final granted = await permissionService.requestCameraPermission();
      if (!granted) {
        if (context.mounted) {
          showPermissionDialog(context, 'Camera');
        }
        return;
      }
    }

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null) {
        onImageSelected(image);
      }
    } catch (e) {
      if (context.mounted) {
        showErrorDialog(context, 'Failed to capture image: $e');
      }
    }
  }

  Future<void> _pickImageFromGallery(BuildContext context) async {
    Navigator.pop(context);

    // Check photo permission using PermissionService
    final permissionService = PermissionService();
    final hasPermission = await permissionService.hasPhotosPermission();

    if (!hasPermission) {
      final granted = await permissionService.requestPhotosPermission();
      if (!granted) {
        if (context.mounted) {
          showPermissionDialog(context, 'Photos');
        }
        return;
      }
    }

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1920,
      );

      if (image != null) {
        onImageSelected(image);
      }
    } catch (e) {
      if (context.mounted) {
        showErrorDialog(context, 'Failed to pick image: $e');
      }
    }
  }

  Future<void> _pickFile(BuildContext context) async {
    Navigator.pop(context);

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        allowedExtensions: null,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // Check file size (limit to 50MB)
        if (file.size > 50 * 1024 * 1024) {
          if (context.mounted) {
            showErrorDialog(context, 'File size must be less than 50MB');
          }
          return;
        }

        onFileSelected(file);
      }
    } catch (e) {
      if (context.mounted) {
        showErrorDialog(context, 'Failed to pick file: $e');
      }
    }
  }

  Future<void> _pickVideo(BuildContext context) async {
    Navigator.pop(context);

    // Check media permissions for video gallery access
    final permissionService = PermissionService();
    final hasPermission = await permissionService.hasMediaPermissions();

    if (!hasPermission) {
      final granted = await permissionService.requestMediaPermissions();
      if (!granted) {
        if (context.mounted) {
          showPermissionDialog(context, 'Media');
        }
        return;
      }
    }

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? video = await picker.pickVideo(
        source: ImageSource.gallery, // Select from gallery, not camera
        maxDuration: const Duration(minutes: 10), // 10 minute limit for uploads
      );

      if (video != null) {
        onVideoSelected(video);
      }
    } catch (e) {
      if (context.mounted) {
        showErrorDialog(context, 'Failed to select video: $e');
      }
    }
  }

  Future<void> _shareLocation(BuildContext context) async {
    print('🗺️ [LOCATION] Starting location sharing process');
    Navigator.pop(context);

    // Store the context for dialog management
    BuildContext? dialogContext;
    bool isDialogShowing = false;

    try {
      print('🗺️ [LOCATION] Checking location permission...');
      // Check location permission using PermissionService
      final permissionService = PermissionService();
      final hasPermission = await permissionService.hasLocationPermission();
      print('🗺️ [LOCATION] Has location permission: $hasPermission');

      if (!hasPermission) {
        print('🗺️ [LOCATION] Requesting location permission...');
        final granted = await permissionService.requestLocationPermission();
        print('🗺️ [LOCATION] Permission request result: $granted');
        if (!granted) {
          print('🗺️ [LOCATION] Permission denied - showing permission dialog');
          if (context.mounted) {
            showPermissionDialog(context, 'Location');
          }
          return;
        }
      }
      print('🗺️ [LOCATION] Permission check completed successfully');

      // Show loading dialog and store its context
      if (context.mounted) {
        print('🗺️ [LOCATION] Showing loading dialog');
        isDialogShowing = true;
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext ctx) {
            dialogContext = ctx;
            return const AlertDialog(
              backgroundColor: AppColors.splashColor,
              content: Row(
                children: [
                  CircularProgressIndicator(color: AppColors.appColor),
                  SizedBox(width: 16),
                  Text(
                    'Getting location...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            );
          },
        );
      } else {
        print(
          '🗺️ [LOCATION] Context not mounted - cannot show loading dialog',
        );
      }

      print('🗺️ [LOCATION] Getting current position...');
      // Get current position
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );
      print(
        '🗺️ [LOCATION] Position obtained: lat=${position.latitude}, lng=${position.longitude}, accuracy=${position.accuracy}m',
      );

      // Get address from coordinates
      String address = 'Shared Location';
      try {
        print('🗺️ [LOCATION] Starting reverse geocoding...');
        final placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );
        print(
          '🗺️ [LOCATION] Geocoding completed, found ${placemarks.length} placemarks',
        );

        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          print(
            '🗺️ [LOCATION] First placemark: name=${placemark.name}, street=${placemark.street}, locality=${placemark.locality}, country=${placemark.country}',
          );

          final addressParts = [
            placemark.name,
            placemark.street,
            placemark.locality,
            placemark.country,
          ].where(
            (element) => element != null && element.toString().isNotEmpty,
          );

          if (addressParts.isNotEmpty) {
            address = addressParts.join(', ');
            print('🗺️ [LOCATION] Resolved address: $address');
          } else {
            print('🗺️ [LOCATION] No valid address parts found, using default');
          }
        } else {
          print('🗺️ [LOCATION] No placemarks found, using default address');
        }
      } catch (e) {
        print('🗺️ [LOCATION] Geocoding failed: $e');
        // Use default address if geocoding fails
        address = 'Shared Location';
      }

      // Prepare location data first
      print('🗺️ [LOCATION] Preparing location data...');
      final locationData = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'address': address,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      print('🗺️ [LOCATION] Location data: $locationData');

      // Close loading dialog using stored context
      if (isDialogShowing && dialogContext != null && dialogContext!.mounted) {
        print('🗺️ [LOCATION] Closing loading dialog');
        Navigator.of(dialogContext!).pop();
        isDialogShowing = false;
      } else {
        print(
          '🗺️ [LOCATION] Loading dialog already closed or context not mounted',
        );
      }

      // Send location data - use a small delay to ensure dialog is closed
      print('🗺️ [LOCATION] Sending location data to callback...');
      try {
        // Small delay to ensure dialog is fully closed
        await Future.delayed(const Duration(milliseconds: 100));
        onLocationSelected(locationData);
        print('🗺️ [LOCATION] Location sharing completed successfully!');
      } catch (e) {
        print('🗺️ [LOCATION] Error calling onLocationSelected: $e');
      }
    } catch (e) {
      print('🗺️ [LOCATION] Location sharing failed with error: $e');
      print('🗺️ [LOCATION] Error type: ${e.runtimeType}');

      // Close loading dialog if it's open
      if (isDialogShowing && dialogContext != null && dialogContext!.mounted) {
        print('🗺️ [LOCATION] Closing loading dialog due to error');
        Navigator.of(dialogContext!).pop();
        isDialogShowing = false;
      }

      if (context.mounted) {
        print('🗺️ [LOCATION] Showing error dialog to user');
        showErrorDialog(context, 'Failed to get location: $e');
      } else {
        print('🗺️ [LOCATION] Context not mounted - cannot show error dialog');
      }
    }
  }

  void showPermissionDialog(BuildContext context, String permission) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.splashColor,
            title: Text(
              '$permission Permission Required',
              style: const TextStyle(color: Colors.white),
            ),
            content: Text(
              'Please grant $permission permission to use this feature.',
              style: const TextStyle(color: Colors.white70),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  openAppSettings();
                },
                child: const Text('Settings'),
              ),
            ],
          ),
    );
  }

  void showErrorDialog(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }
}
