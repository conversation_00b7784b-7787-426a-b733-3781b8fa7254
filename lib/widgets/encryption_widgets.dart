import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget to display encryption status indicator
class EncryptionStatusIndicator extends StatelessWidget {
  final bool isEncrypted;
  final VoidCallback? onTap;
  final double size;

  const EncryptionStatusIndicator({
    super.key,
    required this.isEncrypted,
    this.onTap,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color:
              isEncrypted
                  ? Colors.green.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          isEncrypted ? Icons.lock : Icons.lock_open,
          size: size,
          color: isEncrypted ? Colors.green : Colors.grey,
        ),
      ),
    );
  }
}

/// Widget for encryption toggle switch
class EncryptionToggle extends StatelessWidget {
  final bool isEncrypted;
  final bool isLoading;
  final ValueChanged<bool> onChanged;
  final String? title;
  final String? subtitle;

  const EncryptionToggle({
    super.key,
    required this.isEncrypted,
    required this.onChanged,
    this.isLoading = false,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        isEncrypted ? Icons.lock : Icons.lock_open,
        color: isEncrypted ? Colors.green : Colors.grey,
      ),
      title: Text(title ?? 'End-to-End Encryption'),
      subtitle: subtitle != null ? Text(subtitle!) : null,
      trailing:
          isLoading
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : Switch(
                value: isEncrypted,
                onChanged: onChanged,
                activeColor: Colors.green,
              ),
    );
  }
}

/// Dialog for enabling encryption with password option
class EnableEncryptionDialog extends StatefulWidget {
  final ValueChanged<String> onConfirm;
  final VoidCallback onCancel;
  final ValueChanged<String?>? onPasswordChanged;

  const EnableEncryptionDialog({
    super.key,
    required this.onConfirm,
    required this.onCancel,
    this.onPasswordChanged,
  });

  @override
  State<EnableEncryptionDialog> createState() => _EnableEncryptionDialogState();
}

class _EnableEncryptionDialogState extends State<EnableEncryptionDialog> {
  final TextEditingController _passwordController = TextEditingController();
  bool _showPassword = false;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.lock, color: Colors.green),
          SizedBox(width: 8),
          Text('Enable Encryption'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'This will enable end-to-end encryption for this chat. Messages will be encrypted and only participants can read them.',
          ),
          const SizedBox(height: 16),
          const Text(
            'Please set a strong encryption password:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _passwordController,
            obscureText: !_showPassword,
            decoration: InputDecoration(
              labelText: 'Encryption Password *',
              hintText: 'Enter a strong password',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(
                  _showPassword ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _showPassword = !_showPassword;
                  });
                },
              ),
            ),
            onChanged: (value) {
              widget.onPasswordChanged?.call(value.isEmpty ? null : value);
            },
          ),
          const SizedBox(height: 8),
          const Text(
            '⚠️ Remember this password - you\'ll need it to decrypt messages.',
            style: TextStyle(color: Colors.orange, fontSize: 12),
          ),
        ],
      ),
      actions: [
        TextButton(onPressed: widget.onCancel, child: const Text('Cancel')),
        ElevatedButton(
          onPressed: () {
            if (_passwordController.text.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please enter a password'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }
       
            final password = _passwordController.text;
            widget.onPasswordChanged?.call(password);
            widget.onConfirm(password);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          child: const Text('Enable'),
        ),
      ],
    );
  }
}

/// Dialog for disabling encryption
class DisableEncryptionDialog extends StatelessWidget {
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const DisableEncryptionDialog({
    super.key,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.lock_open, color: Colors.orange),
          SizedBox(width: 8),
          Text('Disable Encryption'),
        ],
      ),
      content: const Text(
        'This will disable end-to-end encryption for this chat. New messages will not be encrypted. Are you sure?',
      ),
      actions: [
        TextButton(onPressed: onCancel, child: const Text('Cancel')),
        ElevatedButton(
          onPressed: onConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
          child: const Text('Disable'),
        ),
      ],
    );
  }
}

/// Widget to show encryption key/password sharing options
class EncryptionKeyShareWidget extends StatelessWidget {
  final String chatRoomId;
  final String? encryptionPassword;
  final VoidCallback? onShareKey;

  const EncryptionKeyShareWidget({
    super.key,
    required this.chatRoomId,
    this.encryptionPassword,
    this.onShareKey,
  });

  void _copyPasswordToClipboard(BuildContext context) {
    if (encryptionPassword != null) {
      Clipboard.setData(ClipboardData(text: encryptionPassword!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Encryption password copied to clipboard'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (encryptionPassword == null) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.key, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'Encryption Key',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Share this password with other participants to enable encryption for them:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      encryptionPassword!,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _copyPasswordToClipboard(context),
                    icon: const Icon(Icons.copy),
                    tooltip: 'Copy password',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '⚠️ Keep this password secure and share it only with trusted participants.',
              style: TextStyle(color: Colors.orange, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
