import 'package:flutter/material.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/contact_service.dart';
import 'package:tolk/services/chat_service.dart';
import 'dart:developer' as developer;

class ContactProvider extends ChangeNotifier {
  final ContactService _contactService = ContactService();
  final ChatService _chatService = ChatService();

  // Contact data
  List<ContactModel> _savedContacts = [];
  List<UserModel> _contactedUsers = [];

  // Loading states
  bool _isLoading = false;
  bool _hasLoadedOnce = false;
  bool _hasContactsPermission = false;
  bool _isLoadingRegistrationStatus = false;

  // Error handling
  String? _errorMessage;

  // Getters
  List<ContactModel> get savedContacts => _savedContacts;
  List<UserModel> get contactedUsers => _contactedUsers;
  bool get isLoading => _isLoading;
  bool get hasLoadedOnce => _hasLoadedOnce;
  bool get hasContactsPermission => _hasContactsPermission;
  bool get isLoadingRegistrationStatus => _isLoadingRegistrationStatus;
  String? get errorMessage => _errorMessage;

  /// Load contacts in background (non-blocking)
  Future<void> loadContactsInBackground() async {
    // Don't load if already loaded or currently loading
    if (_hasLoadedOnce || _isLoading) {
      developer.log(
        '⏭️ ContactProvider: Skipping load - hasLoadedOnce: $_hasLoadedOnce, isLoading: $_isLoading',
        name: 'ContactProvider',
      );
      return;
    }

    developer.log(
      '🚀 ContactProvider: Starting background contact loading...',
      name: 'ContactProvider',
    );

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Always load contacted users (users with existing chat rooms) - no permission needed
      await _loadContactedUsers();

      // Check permission for device contacts
      _hasContactsPermission = await _contactService.hasContactsPermission();
      developer.log(
        '� ContactProvider: Has contacts permission: $_hasContactsPermission',
        name: 'ContactProvider',
      );

      // Load device contacts only if permission is granted
      if (_hasContactsPermission) {
        developer.log(
          '� ContactProvider: Loading device contacts with permission...',
          name: 'ContactProvider',
        );
        await _loadDeviceContacts();
      } else {
        developer.log(
          '🚫 ContactProvider: No contacts permission, skipping device contacts',
          name: 'ContactProvider',
        );
      }

      _hasLoadedOnce = true;
      developer.log(
        '✅ ContactProvider: Background loading completed successfully',
        name: 'ContactProvider',
      );
    } catch (e) {
      _errorMessage = e.toString();
      developer.log(
        '❌ ContactProvider: Error in background loading: $e',
        name: 'ContactProvider',
      );
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load contacted users (users with existing chat rooms)
  Future<void> _loadContactedUsers() async {
    try {
      developer.log(
        '💬 ContactProvider: Loading contacted users...',
        name: 'ContactProvider',
      );

      final chatRooms = await _chatService.getChatRooms().first;
      final currentUserId = _chatService.currentUserId;

      if (currentUserId.isEmpty) {
        developer.log(
          '❌ ContactProvider: No current user found',
          name: 'ContactProvider',
        );
        return;
      }

      final contactedUserIds =
          chatRooms
              .map((room) => room.getOtherParticipant(currentUserId))
              .toSet();

      developer.log(
        '📊 ContactProvider: Found ${contactedUserIds.length} chat rooms',
        name: 'ContactProvider',
      );

      final contactedUsers = <UserModel>[];
      for (final userId in contactedUserIds) {
        final user = await _chatService.getUserById(userId);
        if (user != null) {
          contactedUsers.add(user);
        }
      }

      _contactedUsers = contactedUsers;
      developer.log(
        '👥 ContactProvider: Loaded ${_contactedUsers.length} contacted users',
        name: 'ContactProvider',
      );
    } catch (e) {
      developer.log(
        '❌ ContactProvider: Error loading contacted users: $e',
        name: 'ContactProvider',
      );
      rethrow;
    }
  }

  /// Load device contacts with immediate display and background registration check
  Future<void> _loadDeviceContacts() async {
    try {
      developer.log(
        '📱 ContactProvider: Loading device contacts...',
        name: 'ContactProvider',
      );

      // Get fresh contacts from device
      final deviceContacts = await _contactService.getDeviceContacts();
      developer.log(
        '📊 ContactProvider: Got ${deviceContacts.length} device contacts',
        name: 'ContactProvider',
      );

      if (deviceContacts.isNotEmpty) {
        // STEP 1: Show all contacts immediately (without registration status)
        _savedContacts =
            deviceContacts
                .map(
                  (contact) => contact.copyWith(
                    isRegistered: false,
                  ), // Default to not registered
                )
                .toList();

        developer.log(
          '⚡ ContactProvider: Showing ${_savedContacts.length} contacts immediately',
          name: 'ContactProvider',
        );

        // Notify listeners to show contacts immediately
        notifyListeners();

        // STEP 2: Check registration status in background
        _isLoadingRegistrationStatus = true;
        notifyListeners();

        developer.log(
          '🔍 ContactProvider: Checking registration status in background...',
          name: 'ContactProvider',
        );

        // Check which contacts are registered users
        final contactsWithRegistration = await _contactService
            .checkRegisteredContacts(deviceContacts);

        // STEP 3: Update with registration status
        _savedContacts = contactsWithRegistration;
        _isLoadingRegistrationStatus = false;

        developer.log(
          '✅ ContactProvider: Updated ${_savedContacts.length} contacts with registration status',
          name: 'ContactProvider',
        );
      } else {
        developer.log(
          '📭 ContactProvider: No device contacts found',
          name: 'ContactProvider',
        );
        _savedContacts = [];
      }
    } catch (e) {
      developer.log(
        '❌ ContactProvider: Error loading device contacts: $e',
        name: 'ContactProvider',
      );
      _savedContacts = [];
      _isLoadingRegistrationStatus = false;
      rethrow;
    }
  }

  /// Refresh contacts (force reload)
  Future<void> refreshContacts() async {
    developer.log(
      '🔄 ContactProvider: Refreshing contacts...',
      name: 'ContactProvider',
    );

    _hasLoadedOnce = false;
    _savedContacts.clear();
    _contactedUsers.clear();
    await loadContactsInBackground();
  }

  /// Check and update contacts permission status
  Future<void> updateContactsPermission() async {
    final hasPermission = await _contactService.hasContactsPermission();
    if (hasPermission != _hasContactsPermission) {
      _hasContactsPermission = hasPermission;
      developer.log(
        '🔐 ContactProvider: Permission status updated: $hasPermission',
        name: 'ContactProvider',
      );

      // If permission was just granted, start contact loading process
      if (hasPermission) {
        developer.log(
          '📱 ContactProvider: Permission granted, starting contact loading...',
          name: 'ContactProvider',
        );

        // Reset loading state to allow fresh loading with permission
        _hasLoadedOnce = false;

        // Start background loading now that permission is granted
        await loadContactsInBackground();
      } else {
        developer.log(
          '🚫 ContactProvider: Permission revoked, clearing device contacts...',
          name: 'ContactProvider',
        );

        // Clear device contacts if permission was revoked
        _savedContacts.clear();
      }

      notifyListeners();
    }
  }

  /// Clear all cached data
  void clearCache() {
    developer.log(
      '🗑️ ContactProvider: Clearing cache...',
      name: 'ContactProvider',
    );

    _savedContacts.clear();
    _contactedUsers.clear();
    _hasLoadedOnce = false;
    _isLoading = false;
    _isLoadingRegistrationStatus = false;
    _errorMessage = null;
    notifyListeners();
  }
}
