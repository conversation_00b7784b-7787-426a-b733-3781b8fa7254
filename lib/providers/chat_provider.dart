import 'package:flutter/material.dart';
import 'package:tolk/models/chat_models.dart';
import 'package:tolk/services/chat_service.dart';
import 'dart:async';

class ChatProvider extends ChangeNotifier {
  final ChatService _chatService = ChatService();

  // Chat-specific state
  final Map<String, List<Message>> _chatMessages = {};
  final Map<String, bool> _isLoadingMore = {};
  final Map<String, bool> _hasMoreMessages = {};
  final Map<String, StreamSubscription<List<Message>>?> _messageStreams = {};

  static const int _messagesPerPage = 15;

  // Getters for specific chat room
  List<Message> getMessages(String chatRoomId) {
    return _chatMessages[chatRoomId] ?? [];
  }

  bool isLoadingMore(String chatRoomId) {
    return _isLoadingMore[chatRoomId] ?? false;
  }

  bool hasMoreMessages(String chatRoomId) {
    return _hasMoreMessages[chatRoomId] ?? true;
  }

  // Initialize chat room
  void initializeChatRoom(String chatRoomId) {
    if (_messageStreams.containsKey(chatRoomId)) {
      return; // Already initialized
    }

    _chatMessages[chatRoomId] = [];
    _isLoadingMore[chatRoomId] = false;
    _hasMoreMessages[chatRoomId] = true;

    // Start listening to new messages
    _startMessageStream(chatRoomId);
  }

  // Start listening to message stream for a chat room
  void _startMessageStream(String chatRoomId) {
    _messageStreams[chatRoomId] = _chatService
        .getMessagesPaginated(chatRoomId, limit: _messagesPerPage)
        .listen((latestMessages) {
          _mergeMessages(chatRoomId, latestMessages);
        });
  }

  // Merge new messages with existing ones
  void _mergeMessages(String chatRoomId, List<Message> latestMessages) {
    if (latestMessages.isEmpty) return;

    final existingMessages = _chatMessages[chatRoomId] ?? [];

    // Create a map of existing messages for quick lookup by ID
    final Map<String, Message> existingMessagesMap = {
      for (var msg in existingMessages) msg.id: msg,
    };

    // Process latest messages: update existing or add new
    for (final latestMsg in latestMessages) {
      if (existingMessagesMap.containsKey(latestMsg.id)) {
        // If message with this ID exists, update it in the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      } else {
        // If it's a new message, add it to the map
        existingMessagesMap[latestMsg.id] = latestMsg;
      }
    }

    // Convert map back to a list and sort by timestamp (newest first for reverse ListView)
    final mergedMessages = existingMessagesMap.values.toList();
    mergedMessages.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    _chatMessages[chatRoomId] = mergedMessages;
    notifyListeners();
  }

  // Load more messages for pagination
  Future<void> loadMoreMessages(String chatRoomId) async {
    if (_isLoadingMore[chatRoomId] == true ||
        _hasMoreMessages[chatRoomId] == false ||
        (_chatMessages[chatRoomId]?.isEmpty ?? true)) {
      return;
    }

    _isLoadingMore[chatRoomId] = true;
    notifyListeners();

    try {
      final currentMessages = _chatMessages[chatRoomId]!;
      final lastMessage = currentMessages.last;

      final moreMessages = await _chatService.loadMoreMessages(
        chatRoomId,
        lastMessage.timestamp,
        limit: _messagesPerPage,
      );

      if (moreMessages.isNotEmpty) {
        // Add more messages to existing list
        final updatedMessages = List<Message>.from(currentMessages);
        updatedMessages.addAll(moreMessages);
        _chatMessages[chatRoomId] = updatedMessages;
        _hasMoreMessages[chatRoomId] = moreMessages.length == _messagesPerPage;
      } else {
        _hasMoreMessages[chatRoomId] = false;
      }
    } catch (e) {
      debugPrint('Error loading more messages: $e');
    } finally {
      _isLoadingMore[chatRoomId] = false;
      notifyListeners();
    }
  }

  // Add a new message optimistically (for immediate UI update)
  void addMessageOptimistically(String chatRoomId, Message message) {
    final currentMessages = _chatMessages[chatRoomId] ?? [];
    final updatedMessages = [message, ...currentMessages];
    _chatMessages[chatRoomId] = updatedMessages;
    notifyListeners();
  }

  // Update message status (for delivery/read receipts)
  void updateMessageStatus(
    String chatRoomId,
    String messageId,
    MessageStatus status,
  ) {
    final currentMessages = _chatMessages[chatRoomId];
    if (currentMessages == null) return;

    final messageIndex = currentMessages.indexWhere(
      (msg) => msg.id == messageId,
    );
    if (messageIndex == -1) return;

    final updatedMessage = currentMessages[messageIndex].copyWith(
      status: status,
    );
    currentMessages[messageIndex] = updatedMessage;
    _chatMessages[chatRoomId] = List.from(currentMessages);
    notifyListeners();
  }

  // Remove a message (for delete functionality)
  void removeMessage(String chatRoomId, String messageId) {
    final currentMessages = _chatMessages[chatRoomId];
    if (currentMessages == null) return;

    currentMessages.removeWhere((msg) => msg.id == messageId);
    _chatMessages[chatRoomId] = List.from(currentMessages);
    notifyListeners();
  }

  // Refresh chat room (restart message stream to get updated encryption state)
  void refreshChatRoom(String chatRoomId) {
    // Cancel existing stream
    _messageStreams[chatRoomId]?.cancel();
    _messageStreams.remove(chatRoomId);

    // Clear existing messages to force reload
    _chatMessages[chatRoomId] = [];
    _isLoadingMore[chatRoomId] = false;
    _hasMoreMessages[chatRoomId] = true;

    // Restart message stream
    _startMessageStream(chatRoomId);

    // Notify listeners to update UI
    notifyListeners();
  }

  // Clean up resources for a chat room
  void disposeChatRoom(String chatRoomId) {
    _messageStreams[chatRoomId]?.cancel();
    _messageStreams.remove(chatRoomId);
    _chatMessages.remove(chatRoomId);
    _isLoadingMore.remove(chatRoomId);
    _hasMoreMessages.remove(chatRoomId);
  }

  @override
  void dispose() {
    // Cancel all streams
    for (final subscription in _messageStreams.values) {
      subscription?.cancel();
    }
    _messageStreams.clear();
    _chatMessages.clear();
    _isLoadingMore.clear();
    _hasMoreMessages.clear();
    super.dispose();
  }
}
