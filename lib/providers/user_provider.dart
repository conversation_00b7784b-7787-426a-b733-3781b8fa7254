import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/services/chat_service.dart';

class UserProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ChatService _chatService = ChatService();

  UserModel? _currentUser;
  bool _isLoading = false;

  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;

  UserProvider() {
    _auth.authStateChanges().listen((User? user) {
      if (user != null) {
        _fetchCurrentUser();
      } else {
        _currentUser = null;
        notifyListeners();
      }
    });
  }

  Future<void> _fetchCurrentUser() async {
    if (_auth.currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      DocumentSnapshot userDoc =
          await _firestore
              .collection('users')
              .doc(_auth.currentUser!.uid)
              .get();

      if (userDoc.exists) {
        _currentUser = UserModel.fromMap(
          userDoc.data() as Map<String, dynamic>,
        );
      } else {
        // Create user if it doesn't exist
        UserModel newUser = UserModel(
          uid: _auth.currentUser!.uid,
          phoneNumber: _auth.currentUser!.phoneNumber ?? '',
          createdAt: DateTime.now(),
        );

        await _firestore
            .collection('users')
            .doc(_auth.currentUser!.uid)
            .set(newUser.toMap());

        _currentUser = newUser;
      }
    } catch (e) {
      print('Error fetching user: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateProfile({
    String? name,
    String? profilePicture,
    String? status,
  }) async {
    if (_currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      Map<String, dynamic> updateData = {};

      if (name != null) updateData['name'] = name;
      if (profilePicture != null) updateData['profilePicture'] = profilePicture;
      if (status != null) updateData['status'] = status;

      await _firestore
          .collection('users')
          .doc(_currentUser!.uid)
          .update(updateData);

      // Update local user
      _currentUser = _currentUser!.copyWith(
        name: name,
        profilePicture: profilePicture,
        status: status,
      );
    } catch (e) {
      print('Error updating profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateTranslationLanguage(String languageCode) async {
    if (_currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      await _firestore.collection('users').doc(_currentUser!.uid).update({
        'translationLanguage': languageCode,
      });

      // Update local user
      _currentUser = _currentUser!.copyWith(translationLanguage: languageCode);
    } catch (e) {
      print('Error updating translation language: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateOnlineStatus(bool isOnline) async {
    if (_currentUser == null) return;

    try {
      await _chatService.updateUserOnlineStatus(isOnline);

      // Update local user
      _currentUser = _currentUser!.copyWith(
        isOnline: isOnline,
        lastSeen: isOnline ? null : DateTime.now(),
      );

      notifyListeners();
    } catch (e) {
      print('Error updating online status: $e');
    }
  }

  Future<void> updateContactsImported(bool contactsImported) async {
    if (_currentUser == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      await _firestore.collection('users').doc(_currentUser!.uid).update({
        'contactsImported': contactsImported,
      });

      // Update local user
      _currentUser = _currentUser!.copyWith(contactsImported: contactsImported);
    } catch (e) {
      print('Error updating contacts imported status: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      DocumentSnapshot userDoc =
          await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        return UserModel.fromMap(userDoc.data() as Map<String, dynamic>);
      } else {
        return null;
      }
    } catch (e) {
      print('Error fetching user by ID: $e');
      return null;
    }
  }
}
