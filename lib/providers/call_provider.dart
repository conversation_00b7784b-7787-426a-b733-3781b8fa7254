import 'package:flutter/material.dart';
import 'package:tolk/services/call_service.dart';

class CallProvider with ChangeNotifier {
  CallData? _incomingCall;
  bool _isCallScreenOpen = false;

  CallData? get incomingCall => _incomingCall;
  bool get isCallScreenOpen => _isCallScreenOpen;

  void setIncomingCall(CallData? callData) {
    if (_incomingCall?.callId == callData?.callId && _incomingCall?.status == callData?.status) {
      // If it's the same call with the same status, do nothing to avoid unnecessary rebuilds
      return;
    }
    _incomingCall = callData;
    print('📞 [CallProvider] Incoming call set: ${callData?.callId}, Status: ${callData?.status}');
    notifyListeners();
  }

  void clearIncomingCall() {
    if (_incomingCall == null) return; // Avoid unnecessary notifications
    _incomingCall = null;
    print('📞 [CallProvider] Incoming call cleared.');
    notifyListeners();
  }

  void setCallScreenOpen(bool isOpen) {
    _isCallScreenOpen = isOpen;
    print('📞 [CallProvider] Call screen open status: $isOpen');
    notifyListeners();
  }
}