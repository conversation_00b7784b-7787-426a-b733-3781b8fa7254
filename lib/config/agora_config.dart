class AgoraConfig {
  // Replace this with your actual Agora App ID from Agora Console
  static const String appId = '0a2bda31576e46a799d218a5d7c17df9';
  
  // Token server configuration for production
  // For development: leave as null to skip token authentication
  // For production: set to your token server URL or Cloud Function name
  static const String? tokenServerUrl = 'generateAgoraToken'; // Firebase Cloud Function name
  
  // Channel name prefix for generating unique channel names
  static const String channelPrefix = 'tolk_';
  
  // Default UID for local user (can be dynamic)
  static const int defaultUid = 0;
  
  // Video configuration
  static const int videoWidth = 640;
  static const int videoHeight = 360;
  static const int videoFrameRate = 15;
  static const int videoBitrate = 400;
  
  // Audio configuration
  static const int audioSampleRate = 44100;
  static const int audioChannels = 2;
  
  // Call timeout duration (in seconds)
  static const int callTimeoutDuration = 30;
}