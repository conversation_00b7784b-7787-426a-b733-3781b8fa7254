import 'package:flutter/material.dart';


double getDeviceHeightByPercent(BuildContext context, double percent) {
  return MediaQuery.of(context).size.height * (percent / 100);
}

double getDeviceWidthByPercent(BuildContext context, double percent) {
  return MediaQuery.of(context).size.width * (percent / 100);
}

void pushScreen(BuildContext context, Widget screen) {
  Navigator.push(
    context,
    MaterialPageRoute(builder: (context) => screen),
  );
}

void pushAndRemoveScreen(BuildContext context, Widget screen) {
  Navigator.pushAndRemoveUntil(
    context,
    MaterialPageRoute(builder: (context) => screen),
    (Route<dynamic> route) => false,
  );
}

