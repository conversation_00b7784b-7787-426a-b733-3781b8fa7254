import 'package:flutter/services.dart';

class AppColors {
  static const Color appColor = Color(
    0xFF1C56D3,
  ); // Primary blue, can remain for accent
  static const Color colorSecondary = Color(
    0xFFEC7938,
  ); // Accent orange, can remain
  static const Color dialogColor = Color(0xFF242225); // Dark dialog background
  static const Color splashColor = Color(0xFF242225); // Splash background dark
  static const Color otpFieldColor = Color(0xFF23272F); // OTP field dark
  static const Color bottomSheetBackgroundColor = Color(
    0xFF23272F,
  ); // Bottom sheet dark
  static const Color textSecondaryColor = Color(
    0xFFB0B3B8,
  ); // Light gray for secondary text
  static const Color buttonPrimaryColor = Color(
    0xFF1B82CC,
  ); // Accent blue, can remain
  static const Color onboardingColor = Color(
    0xFF242225,
  ); // Very dark for onboarding backgrounds
  static const Color warningRed = Color(0xffF00606); // Red for warnings
}
