import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  // Cache for Android version check
  int? _androidSdkVersion;

  // Core permissions required for the app
  static const List<Permission> _corePermissions = [
    Permission.camera, // Still needed for photo capture
    Permission.microphone, // Needed for voice messages
    Permission.notification,
    Permission.contacts, // Needed for contact search and import
  ];

  // Get Android SDK version
  Future<int> _getAndroidSdkVersion() async {
    if (_androidSdkVersion != null) return _androidSdkVersion!;

    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      _androidSdkVersion = androidInfo.version.sdkInt;
      return _androidSdkVersion!;
    }
    return 0; // Not Android
  }

  // Get appropriate media permissions based on Android version
  Future<List<Permission>> _getMediaPermissions() async {
    if (!Platform.isAndroid) {
      return [Permission.photos]; // iOS uses photos permission
    }

    final sdkVersion = await _getAndroidSdkVersion();

    if (sdkVersion >= 33) {
      // Android 13+ (API 33+) - Use granular media permissions
      return [
        Permission.photos, // For image picker compatibility
        Permission.videos, // For video access
        // Note: Permission.audio is for music files, not needed for our use case
      ];
    } else {
      // Android 12 and below - Use legacy storage permission
      return [
        Permission.storage, // Legacy permission for older Android versions
        Permission.photos, // Also request photos for compatibility
      ];
    }
  }

  /// Check if all required permissions are granted
  Future<bool> areAllPermissionsGranted() async {
    List<Permission> permissionsToCheck = List.from(_corePermissions);

    // Add media permissions based on Android version
    final mediaPermissions = await _getMediaPermissions();
    permissionsToCheck.addAll(mediaPermissions);

    for (Permission permission in permissionsToCheck) {
      final status = await permission.status;
      if (!status.isGranted) {
        return false;
      }
    }
    return true;
  }

  /// Request all required permissions
  Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    List<Permission> permissionsToRequest = List.from(_corePermissions);

    // Add media permissions based on Android version
    final mediaPermissions = await _getMediaPermissions();
    permissionsToRequest.addAll(mediaPermissions);

    return await permissionsToRequest.request();
  }

  /// Get the status of all permissions
  Future<Map<Permission, PermissionStatus>> getPermissionStatuses() async {
    List<Permission> permissionsToCheck = List.from(_corePermissions);

    // Add media permissions based on Android version
    final mediaPermissions = await _getMediaPermissions();
    permissionsToCheck.addAll(mediaPermissions);

    Map<Permission, PermissionStatus> statuses = {};
    for (Permission permission in permissionsToCheck) {
      statuses[permission] = await permission.status;
    }
    return statuses;
  }

  /// Check and request permissions with user-friendly dialog
  Future<bool> checkAndRequestPermissions(BuildContext context) async {
    // First check if all permissions are already granted
    if (await areAllPermissionsGranted()) {
      return true;
    }

    // Show explanation dialog
    bool shouldRequest = await _showPermissionExplanationDialog(context);
    if (!shouldRequest) {
      return false;
    }

    // Request permissions
    final results = await requestAllPermissions();

    // Check if all permissions were granted
    bool allGranted = true;
    List<String> deniedPermissions = [];

    results.forEach((permission, status) {
      if (!status.isGranted) {
        allGranted = false;
        deniedPermissions.add(_getPermissionName(permission));
      }
    });

    if (!allGranted) {
      await _showPermissionDeniedDialog(context, deniedPermissions);
      return false;
    }

    return true;
  }

  /// Check if location permission is granted
  Future<bool> hasLocationPermission() async {
    final status = await Permission.location.status;
    return status.isGranted;
  }

  /// Request location permission
  Future<bool> requestLocationPermission() async {
    final status = await Permission.location.request();
    return status.isGranted;
  }

  /// Check if camera permission is granted
  Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  /// Check if microphone permission is granted
  Future<bool> hasMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  /// Check if photos permission is granted (handles Android version differences)
  Future<bool> hasPhotosPermission() async {
    if (!Platform.isAndroid) {
      final status = await Permission.photos.status;
      return status.isGranted;
    }

    final sdkVersion = await _getAndroidSdkVersion();

    if (sdkVersion >= 33) {
      // Android 13+ - Check granular media permissions
      final photosStatus = await Permission.photos.status;
      return photosStatus.isGranted;
    } else {
      // Android 12 and below - Check legacy storage permission
      final storageStatus = await Permission.storage.status;
      final photosStatus = await Permission.photos.status;
      return storageStatus.isGranted || photosStatus.isGranted;
    }
  }

  /// Request photos permission (handles Android version differences)
  Future<bool> requestPhotosPermission() async {
    if (!Platform.isAndroid) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }

    final sdkVersion = await _getAndroidSdkVersion();

    if (sdkVersion >= 33) {
      // Android 13+ - Request granular media permissions
      final photosStatus = await Permission.photos.request();
      return photosStatus.isGranted;
    } else {
      // Android 12 and below - Request legacy storage permission
      final storageStatus = await Permission.storage.request();
      if (storageStatus.isGranted) return true;

      // Fallback to photos permission
      final photosStatus = await Permission.photos.request();
      return photosStatus.isGranted;
    }
  }

  /// Check if videos permission is granted (Android 13+ specific)
  Future<bool> hasVideosPermission() async {
    if (!Platform.isAndroid) {
      return await hasPhotosPermission(); // iOS uses photos for videos too
    }

    final sdkVersion = await _getAndroidSdkVersion();

    if (sdkVersion >= 33) {
      // Android 13+ - Check videos permission
      final videosStatus = await Permission.videos.status;
      return videosStatus.isGranted;
    } else {
      // Android 12 and below - Use storage permission
      return await hasPhotosPermission();
    }
  }

  /// Request videos permission (Android 13+ specific)
  Future<bool> requestVideosPermission() async {
    if (!Platform.isAndroid) {
      return await requestPhotosPermission(); // iOS uses photos for videos too
    }

    final sdkVersion = await _getAndroidSdkVersion();

    if (sdkVersion >= 33) {
      // Android 13+ - Request videos permission
      final videosStatus = await Permission.videos.request();
      return videosStatus.isGranted;
    } else {
      // Android 12 and below - Use storage permission
      return await requestPhotosPermission();
    }
  }

  /// Check if media permissions (photos + videos) are granted
  Future<bool> hasMediaPermissions() async {
    final hasPhotos = await hasPhotosPermission();
    final hasVideos = await hasVideosPermission();
    return hasPhotos && hasVideos;
  }

  /// Request media permissions (photos + videos)
  Future<bool> requestMediaPermissions() async {
    final photosGranted = await requestPhotosPermission();
    final videosGranted = await requestVideosPermission();
    return photosGranted && videosGranted;
  }

  /// Check if contacts permission is granted
  Future<bool> hasContactsPermission() async {
    final status = await Permission.contacts.status;
    return status.isGranted;
  }

  /// Request contacts permission
  Future<bool> requestContactsPermission() async {
    final status = await Permission.contacts.request();
    return status.isGranted;
  }

  /// Show explanation dialog before requesting permissions
  Future<bool> _showPermissionExplanationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: const Color(0xFF242225),
              title: const Row(
                children: [
                  Icon(Icons.security, color: Colors.blue),
                  SizedBox(width: 8),
                  Text(
                    'Permissions Required',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
              content: const Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tolk needs the following permissions to work properly:',
                    style: TextStyle(color: Colors.white70),
                  ),
                  SizedBox(height: 16),
                  _PermissionItem(
                    icon: Icons.camera_alt,
                    title: 'Camera',
                    description: 'Take photos to share in chats',
                  ),
                  _PermissionItem(
                    icon: Icons.mic,
                    title: 'Microphone',
                    description: 'Record voice messages',
                  ),
                  _PermissionItem(
                    icon: Icons.photo_library,
                    title: 'Photos & Media',
                    description: 'Select images and videos to share',
                  ),
                  _PermissionItem(
                    icon: Icons.notifications,
                    title: 'Notifications',
                    description: 'Receive message notifications',
                  ),
                  _PermissionItem(
                    icon: Icons.contacts,
                    title: 'Contacts',
                    description: 'Find friends and import your contacts',
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text(
                    'Not Now',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                  child: const Text(
                    'Grant Permissions',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Show dialog when permissions are denied
  Future<void> _showPermissionDeniedDialog(
    BuildContext context,
    List<String> deniedPermissions,
  ) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF242225),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.orange),
              SizedBox(width: 8),
              Text('Permissions Denied', style: TextStyle(color: Colors.white)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Some features may not work properly without these permissions:',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 12),
              ...deniedPermissions.map(
                (permission) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    '• $permission',
                    style: const TextStyle(color: Colors.white60),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'You can grant these permissions later in Settings.',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
              child: const Text(
                'Open Settings',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Get user-friendly permission name
  String _getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.camera:
        return 'Camera';
      case Permission.microphone:
        return 'Microphone';
      case Permission.photos:
        return 'Photos & Media';
      case Permission.notification:
        return 'Notifications';
      case Permission.videos:
        return 'Videos';
      case Permission.contacts:
        return 'Contacts';
      default:
        return permission.toString().split('.').last;
    }
  }
}

/// Widget for displaying permission items in the dialog
class _PermissionItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;

  const _PermissionItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(color: Colors.white60, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
