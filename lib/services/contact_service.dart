import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tolk/models/contact_model.dart';
import 'package:tolk/models/user_model.dart';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

class ContactService {
  static const String _usersCollection = 'users';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const MethodChannel _channel = MethodChannel('tolk/contacts');

  /// Check if contacts permission is granted
  Future<bool> hasContactsPermission() async {
    final status = await Permission.contacts.status;
    return status.isGranted;
  }

  /// Request contacts permission
  Future<bool> requestContactsPermission() async {
    final status = await Permission.contacts.request();
    return status.isGranted;
  }

  /// Get all contacts from device
  Future<List<ContactModel>> getDeviceContacts() async {
    developer.log('🔍 Starting to get device contacts', name: 'ContactService');

    try {
      // Check permission first
      developer.log('📋 Checking contacts permission', name: 'ContactService');
      if (!await hasContactsPermission()) {
        developer.log(
          '❌ Contacts permission not granted, requesting...',
          name: 'ContactService',
        );
        final granted = await requestContactsPermission();
        if (!granted) {
          developer.log(
            '❌ Contacts permission denied by user',
            name: 'ContactService',
          );
          throw Exception('Contacts permission denied');
        }
        developer.log('✅ Contacts permission granted', name: 'ContactService');
      } else {
        developer.log(
          '✅ Contacts permission already granted',
          name: 'ContactService',
        );
      }

      developer.log(
        '📱 Reading contacts from device...',
        name: 'ContactService',
      );

      // Call platform channel to get REAL device contacts
      developer.log(
        '📱 Reading REAL contacts from device using platform channel...',
        name: 'ContactService',
      );

      try {
        final List<dynamic> rawContacts = await _channel.invokeMethod(
          'getContacts',
        );

        developer.log(
          '📊 Found ${rawContacts.length} REAL contacts on device',
          name: 'ContactService',
        );

        final contactModels = <ContactModel>[];
        int processedCount = 0;
        int skippedCount = 0;

        for (final rawContact in rawContacts) {
          try {
            final contactMap = Map<String, dynamic>.from(rawContact);

            // Skip contacts without phone numbers
            final phones = contactMap['phones'] as List<dynamic>?;
            if (phones == null || phones.isEmpty) {
              skippedCount++;
              continue;
            }

            // Extract and normalize phone numbers
            final phoneNumbers =
                phones
                    .cast<String>()
                    .map((phone) => _normalizePhoneNumber(phone))
                    .where((phone) => phone.isNotEmpty)
                    .toList();

            if (phoneNumbers.isEmpty) {
              skippedCount++;
              continue;
            }

            // Get display name
            String displayName = contactMap['displayName'] as String? ?? '';
            if (displayName.isEmpty) {
              displayName = phoneNumbers.first; // Use phone number as fallback
            }

            // Create contact model
            final contactModel = ContactModel(
              id: contactMap['id'] as String? ?? 'contact_$processedCount',
              displayName: displayName,
              phoneNumbers: phoneNumbers,
              isRegistered:
                  false, // Will be updated later by checkRegisteredContacts
            );

            contactModels.add(contactModel);
            processedCount++;
          } catch (e) {
            developer.log(
              '⚠️ Error processing contact: $e',
              name: 'ContactService',
            );
            skippedCount++;
          }
        }

        developer.log(
          '✅ Successfully processed $processedCount REAL contacts, skipped $skippedCount',
          name: 'ContactService',
        );

        if (contactModels.isNotEmpty) {
          developer.log(
            '📋 REAL Contact names: ${contactModels.map((c) => c.displayName).take(5).join(", ")}${contactModels.length > 5 ? "..." : ""}',
            name: 'ContactService',
          );
        } else {
          developer.log(
            '📭 No valid contacts found on device',
            name: 'ContactService',
          );
        }

        return contactModels;
      } catch (platformError) {
        developer.log(
          '❌ Platform channel error: $platformError',
          name: 'ContactService',
        );

        // If platform channel fails, return empty list
        return [];
      }
    } catch (e) {
      developer.log(
        '❌ Error getting device contacts: $e',
        name: 'ContactService',
      );

      // Return empty list instead of mock data to show real issue
      return [];
    }
  }

  /// Check which contacts are registered users
  Future<List<ContactModel>> checkRegisteredContacts(
    List<ContactModel> contacts,
  ) async {
    try {
      final registeredContacts = <ContactModel>[];

      // Get all phone numbers from contacts
      final allPhoneNumbers = <String>{};
      for (final contact in contacts) {
        allPhoneNumbers.addAll(contact.phoneNumbers);
      }

      if (allPhoneNumbers.isEmpty) return [];

      // Query Firestore for registered users with these phone numbers
      // Note: Firestore has a limit of 10 items in 'whereIn' queries
      // So we need to batch the queries
      final phoneNumberBatches = _batchList(allPhoneNumbers.toList(), 10);
      final registeredUsers = <UserModel>[];

      for (final batch in phoneNumberBatches) {
        final querySnapshot =
            await _firestore
                .collection(_usersCollection)
                .where('phoneNumber', whereIn: batch)
                .get();

        for (final doc in querySnapshot.docs) {
          final user = UserModel.fromMap(doc.data());
          registeredUsers.add(user);
        }
      }

      // Create a map of phone numbers to user IDs
      final phoneToUserId = <String, String>{};
      for (final user in registeredUsers) {
        phoneToUserId[user.phoneNumber] = user.uid;
      }

      // Update contacts with registration status
      for (final contact in contacts) {
        bool isRegistered = false;
        String? userId;

        for (final phoneNumber in contact.phoneNumbers) {
          if (phoneToUserId.containsKey(phoneNumber)) {
            isRegistered = true;
            userId = phoneToUserId[phoneNumber];
            break;
          }
        }

        registeredContacts.add(
          contact.copyWith(isRegistered: isRegistered, userId: userId),
        );
      }

      return registeredContacts;
    } catch (e) {
      print('Error checking registered contacts: $e');
      return contacts; // Return original contacts if check fails
    }
  }

  /// Normalize phone number (remove spaces, dashes, etc.)
  String _normalizePhoneNumber(String phoneNumber) {
    // Remove all non-digit characters except +
    String normalized = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Ensure it starts with + for international format
    if (!normalized.startsWith('+') && normalized.isNotEmpty) {
      // Add country code if missing (assuming US +1 for now)
      // In a real app, you'd want to detect the user's country
      if (normalized.length == 10) {
        normalized = '+1$normalized';
      } else if (normalized.length == 11 && normalized.startsWith('1')) {
        normalized = '+$normalized';
      } else {
        normalized = '+$normalized';
      }
    }

    return normalized;
  }

  /// Helper method to batch a list into smaller chunks
  List<List<T>> _batchList<T>(List<T> list, int batchSize) {
    final batches = <List<T>>[];
    for (int i = 0; i < list.length; i += batchSize) {
      final end = (i + batchSize < list.length) ? i + batchSize : list.length;
      batches.add(list.sublist(i, end));
    }
    return batches;
  }
}
