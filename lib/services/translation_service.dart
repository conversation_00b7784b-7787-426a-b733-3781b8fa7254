import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:tolk/utils/app_strings.dart';

class TranslationService {
  static const String _baseUrl =
      'https://translation.googleapis.com/language/translate/v2';
  static const String _apiKey = AppStrings.googleTranslateKey;

  /// Translates text from source language to target language
  /// If sourceLanguage is null, Google will auto-detect the language
  static Future<String> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
  }) async {
    try {
      // Don't translate if target language is English and text appears to be English
      if (targetLanguage == 'en' && isLikelyEnglish(text)) {
        return text;
      }

      final url = Uri.parse('$_baseUrl?key=$_apiKey');

      final body = {'q': text, 'target': targetLanguage, 'format': 'text'};

      // Add source language if provided
      if (sourceLanguage != null && sourceLanguage.isNotEmpty) {
        body['source'] = sourceLanguage;
      }

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: body,
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        final translations = jsonResponse['data']['translations'] as List;

        if (translations.isNotEmpty) {
          return translations[0]['translatedText'] as String;
        }
      } else {
        print(
          'Translation API Error: ${response.statusCode} - ${response.body}',
        );
      }
    } catch (e) {
      print('Translation Error: $e');
    }

    // Return original text if translation fails
    return text;
  }

  /// Detects the language of the given text
  static Future<String?> detectLanguage(String text) async {
    try {
      const detectUrl =
          'https://translation.googleapis.com/language/translate/v2/detect';
      final url = Uri.parse('$detectUrl?key=$_apiKey');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {'q': text},
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        final detections = jsonResponse['data']['detections'] as List;

        if (detections.isNotEmpty &&
            detections[0] is List &&
            detections[0].isNotEmpty) {
          return detections[0][0]['language'] as String;
        }
      }
    } catch (e) {
      print('Language Detection Error: $e');
    }

    return null;
  }

  /// Simple heuristic to check if text is likely English
  static bool isLikelyEnglish(String text) {
    // Check for common English words
    final commonEnglishWords = [
      'the',
      'and',
      'or',
      'but',
      'in',
      'on',
      'at',
      'to',
      'for',
      'of',
      'with',
      'by',
      'from',
      'up',
      'about',
      'into',
      'through',
      'during',
      'before',
      'after',
      'above',
      'below',
      'between',
      'among',
      'is',
      'are',
      'was',
      'were',
      'be',
      'been',
      'being',
      'have',
      'has',
      'had',
      'do',
      'does',
      'did',
      'will',
      'would',
      'could',
      'should',
      'may',
      'might',
      'must',
      'can',
      'this',
      'that',
      'these',
      'those',
      'i',
      'you',
      'he',
      'she',
      'it',
      'we',
      'they',
      'me',
      'him',
      'her',
      'us',
      'them',
    ];

    final words = text.toLowerCase().split(RegExp(r'\s+'));
    final englishWordCount =
        words
            .where(
              (word) => commonEnglishWords.contains(
                word.replaceAll(RegExp(r'[^\w]'), ''),
              ),
            )
            .length;

    // If more than 30% of words are common English words, consider it English
    return words.isNotEmpty && (englishWordCount / words.length) > 0.3;
  }

  /// Batch translate multiple texts
  static Future<List<String>> translateTexts({
    required List<String> texts,
    required String targetLanguage,
    String? sourceLanguage,
  }) async {
    final results = <String>[];

    // Translate texts in batches to avoid API limits
    const batchSize = 10;
    for (int i = 0; i < texts.length; i += batchSize) {
      final batch = texts.skip(i).take(batchSize).toList();
      final batchResults = await Future.wait(
        batch.map(
          (text) => translateText(
            text: text,
            targetLanguage: targetLanguage,
            sourceLanguage: sourceLanguage,
          ),
        ),
      );
      results.addAll(batchResults);
    }

    return results;
  }
}
