import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tolk/config/agora_config.dart';
import 'package:tolk/services/token_service.dart';

class AgoraService {
  
  late RtcEngine _engine;
  bool _localUserJoined = false;
  int? _remoteUid;
  bool _muted = false;
  bool _videoMuted = false;

  // Getters
  bool get localUserJoined => _localUserJoined;
  int? get remoteUid => _remoteUid;
  bool get muted => _muted;
  bool get videoMuted => _videoMuted;
  RtcEngine get engine => _engine;

  // Initialize Agora engine
  Future<void> initialize() async {
    // Request permissions
    await [Permission.microphone, Permission.camera].request();

    // Create RTC engine
    _engine = createAgoraRtcEngine();
    await _engine.initialize(const RtcEngineContext(
      appId: AgoraConfig.appId,
      channelProfile: ChannelProfileType.channelProfileCommunication,
    ));

    // Enable audio and video by default after initialization
    // Specific call types can then disable video if needed (e.g., voice call)
    await _engine.enableAudio();
    await _engine.enableVideo();
    print("🎤🎥 [AGORA_SERVICE] Audio and Video enabled by default during init.");

    // Register event handlers
    _engine.registerEventHandler(
      RtcEngineEventHandler(
        onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
          print("Local user ${connection.localUid} joined");
          _localUserJoined = true;
        },
        onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
          print("Remote user $remoteUid joined");
          _remoteUid = remoteUid;
        },
        onUserOffline: (RtcConnection connection, int remoteUid,
            UserOfflineReasonType reason) {
          print("Remote user $remoteUid left channel");
          _remoteUid = null;
        },
        onTokenPrivilegeWillExpire: (RtcConnection connection, String token) {
          print('[onTokenPrivilegeWillExpire] connection: ${connection.toJson()}, token: $token');
        },
      ),
    );
  }

  // Join a voice call
  Future<void> joinVoiceCall(String channelName, int uid) async {
    await _engine.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
    await _engine.enableAudio();
    await _engine.disableVideo();
    
    // Generate token for production or use null for development
    final token = await TokenService.generateToken(
      channelName: channelName,
      uid: uid,
      role: 'publisher',
    );
    
    await _engine.joinChannel(
      token: token ?? '',
      channelId: channelName,
      uid: uid,
      options: const ChannelMediaOptions(),
    );
  }

  // Join a video call
  Future<void> joinVideoCall(String channelName, int uid) async {
    await _engine.setClientRole(role: ClientRoleType.clientRoleBroadcaster);
    await _engine.enableAudio();
    await _engine.enableVideo();
    await _engine.startPreview();
    
    // Generate token for production or use null for development
    final token = await TokenService.generateToken(
      channelName: channelName,
      uid: uid,
      role: 'publisher',
    );
    
    await _engine.joinChannel(
      token: token ?? '',
      channelId: channelName,
      uid: uid,
      options: const ChannelMediaOptions(),
    );
  }

  // Leave channel
  Future<void> leaveChannel() async {
    await _engine.stopPreview(); // Stop camera preview
    print("📷 [AGORA_SERVICE] Camera preview stopped.");
    await _engine.leaveChannel();
    _localUserJoined = false;
    _remoteUid = null;
    print("🚪 [AGORA_SERVICE] Left channel.");
  }

  // Toggle microphone
  Future<void> toggleMicrophone() async {
    _muted = !_muted;
    await _engine.muteLocalAudioStream(_muted);
  }

  // Toggle camera
  Future<void> toggleCamera() async {
    _videoMuted = !_videoMuted;
    await _engine.muteLocalVideoStream(_videoMuted);
  }

  // Switch camera
  Future<void> switchCamera() async {
    await _engine.switchCamera();
  }

  // Dispose
  Future<void> dispose() async {
    await _engine.stopPreview(); // Ensure preview is stopped
    print("📷 [AGORA_SERVICE] Camera preview stopped during dispose.");
    await _engine.leaveChannel(); // Ensure channel is left
    print("🚪 [AGORA_SERVICE] Left channel during dispose.");
    await _engine.release();
    print("💥 [AGORA_SERVICE] Engine released.");
  }
}