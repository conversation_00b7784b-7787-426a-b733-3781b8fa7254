import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';

// Added imports for call screens
import '../screens/call/audio_call_screen.dart';
import '../screens/call/video_call_screen.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Global navigation key for handling notifications
  static GlobalKey<NavigatorState>? navigatorKey;

  // Initialize notification service
  Future<void> initialize() async {
    print('🔔 [NOTIFICATION] Initializing notification service...');

    // Request permission for notifications
    await _requestPermission();

    // Initialize local notifications
    await _initializeLocalNotifications();

    // Get and save FCM token
    await _saveFCMToken();

    // Set up message handlers
    _setupMessageHandlers();

    // Set up token refresh listener
    updateFCMToken();

    print('🔔 [NOTIFICATION] Notification service initialized successfully');
  }

  // Request notification permissions
  Future<void> _requestPermission() async {
    print('🔔 [NOTIFICATION] Requesting notification permissions...');

    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print(
      '🔔 [NOTIFICATION] Permission status: ${settings.authorizationStatus}',
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('🔔 [NOTIFICATION] User granted permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      print('🔔 [NOTIFICATION] User granted provisional permission');
    } else {
      print('🔔 [NOTIFICATION] User declined or has not accepted permission');
    }
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    print('🔔 [NOTIFICATION] Initializing local notifications...');

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  // Create notification channel for Android
  Future<void> _createNotificationChannel() async {
    // Chat messages channel
    const AndroidNotificationChannel chatChannel = AndroidNotificationChannel(
      'chat_messages', // Channel ID
      'Chat Messages', // Channel name
      description: 'Notifications for new chat messages',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    // Incoming calls channel with ringing sound
    final AndroidNotificationChannel callChannel = AndroidNotificationChannel(
      'incoming_calls', // Channel ID
      'Incoming Calls', // Channel name
      description: 'Notifications for incoming calls',
      importance: Importance.max,
      sound: const RawResourceAndroidNotificationSound('notification'),
      playSound: true,
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
    );

    final flutterLocalNotificationsPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >();

    await flutterLocalNotificationsPlugin?.createNotificationChannel(chatChannel);
    await flutterLocalNotificationsPlugin?.createNotificationChannel(callChannel);
  }

  // Get and save FCM token
  Future<void> _saveFCMToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      final user = _auth.currentUser;

      if (token != null && user != null) {
        print('🔔 [NOTIFICATION] FCM Token: $token');

        // Save token to Firestore (use set with merge to create field if it doesn't exist)
        await _firestore.collection('users').doc(user.uid).set({
          'fcmToken': token,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        print('🔔 [NOTIFICATION] FCM token saved to Firestore');
      }
    } catch (e) {
      print('🔔 [NOTIFICATION] Error saving FCM token: $e');
    }
  }

  // Set up message handlers
  void _setupMessageHandlers() {
    print('🔔 [NOTIFICATION] Setting up message handlers...');

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    _handleInitialMessage();
  }

  // Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('🔔 [NOTIFICATION] ========== FOREGROUND MESSAGE ==========');
    print('🔔 [NOTIFICATION] Received foreground message: ${message.messageId}');
    print('🔔 [NOTIFICATION] Title: ${message.notification?.title}');
    print('🔔 [NOTIFICATION] Body: ${message.notification?.body}');
    print('🔔 [NOTIFICATION] Data: ${message.data}');
    print('🔔 [NOTIFICATION] Message type: ${message.data['type']}');

    // Show local notification when app is in foreground
    print('🔔 [NOTIFICATION] Showing local notification for foreground message');
    await _showLocalNotification(message);
    print('🔔 [NOTIFICATION] Local notification shown successfully');
  }

  // Handle notification tap
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    print('🔔 [NOTIFICATION] ========== NOTIFICATION TAP ==========');
    print('🔔 [NOTIFICATION] Notification tapped: ${message.messageId}');
    print('🔔 [NOTIFICATION] Full message data: ${message.data}');
    print('🔔 [NOTIFICATION] Message type: ${message.data['type']}');

    // Handle incoming call notifications
    if (message.data['type'] == 'incoming_call') {
      print('🔔 [NOTIFICATION] ✅ Incoming call notification tapped - processing...');
      print('🔔 [NOTIFICATION] Call ID: ${message.data['callId']}');
      print('🔔 [NOTIFICATION] Caller: ${message.data['callerName']}');
      _handleCallNotificationAction(null, message.data);
    }
    // Navigate to chat screen if chat room ID is provided
    else if (message.data.containsKey('chatRoomId')) {
      print('🔔 [NOTIFICATION] Chat notification tapped');
      print(
        '🔔 [NOTIFICATION] Should navigate to chat room: ${message.data['chatRoomId']}',
      );
    } else {
      print('🔔 [NOTIFICATION] ❌ Unknown notification type: ${message.data['type']}');
    }
  }

  // Handle initial message when app is opened from terminated state
  Future<void> _handleInitialMessage() async {
    RemoteMessage? initialMessage =
        await _firebaseMessaging.getInitialMessage();

    if (initialMessage != null) {
      print(
        '🔔 [NOTIFICATION] App opened from terminated state via notification',
      );
      await _handleNotificationTap(initialMessage);
    }
  }

  // Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    print('🔔 [NOTIFICATION] ========== SHOW LOCAL NOTIFICATION ==========');
    print('🔔 [NOTIFICATION] Message type: ${message.data['type']}');
    
    // Check if it's an incoming call notification
    final isIncomingCall = message.data['type'] == 'incoming_call';
    print('🔔 [NOTIFICATION] Is incoming call: $isIncomingCall');
    
    if (isIncomingCall) {
      print('🔔 [NOTIFICATION] Showing incoming call notification');
      await _showIncomingCallNotification(message);
      print('🔔 [NOTIFICATION] Incoming call notification displayed');
    } else {
      print('🔔 [NOTIFICATION] Showing chat notification');
      await _showChatNotification(message);
      print('🔔 [NOTIFICATION] Chat notification displayed');
    }
  }

  // Show chat notification
  Future<void> _showChatNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          'chat_messages',
          'Chat Messages',
          channelDescription: 'Notifications for new chat messages',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
          icon: '@mipmap/ic_launcher',
        );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'New Message',
      message.notification?.body ?? 'You have a new message',
      details,
      payload: jsonEncode(message.data),
    );
  }

  // Show incoming call notification with actions
  Future<void> _showIncomingCallNotification(RemoteMessage message) async {
    print('🔔 [NOTIFICATION] ========== INCOMING CALL NOTIFICATION ==========');
    final callId = message.data['callId'] ?? '';
    final callerName = message.data['callerName'] ?? 'Unknown';
    final callType = message.data['callType'] ?? 'audio';
    
    print('🔔 [NOTIFICATION] Creating call notification:');
    print('🔔 [NOTIFICATION] - Call ID: $callId');
    print('🔔 [NOTIFICATION] - Caller: $callerName');
    print('🔔 [NOTIFICATION] - Type: $callType');
    print('🔔 [NOTIFICATION] - Full data: ${message.data}');
    
    final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'incoming_calls',
      'Incoming Calls',
      channelDescription: 'Notifications for incoming calls',
      importance: Importance.max,
      priority: Priority.max,
      // category: AndroidNotificationCategory.call, // Temporarily disable for testing actions
      // fullScreenIntent: true, // Temporarily disable for testing actions
      showWhen: true,
      autoCancel: false, // Keep false for call notifications unless explicitly handled
      ongoing: true,
      icon: '@mipmap/ic_launcher',
      largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
   
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      categoryIdentifier: 'INCOMING_CALL',
      interruptionLevel: InterruptionLevel.critical,
    );

    final NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    print('🔔 [NOTIFICATION] Showing notification with ID: ${callId.hashCode}');
    await _localNotifications.show(
      callId.hashCode,
      '📞 Incoming ${callType.toUpperCase()} Call',
      '$callerName is calling you',
      details,
      payload: jsonEncode(message.data),
    );
    print('🔔 [NOTIFICATION] ✅ Call notification displayed successfully');
  }

  // Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    print('🔔 [NOTIFICATION] ========== LOCAL NOTIFICATION TAPPED ==========');
    print('🔔 [NOTIFICATION] Action ID: ${response.actionId}');
    print('🔔 [NOTIFICATION] Notification ID: ${response.id}');
    print('🔔 [NOTIFICATION] Input: ${response.input}');

    if (response.payload != null) {
      try {
        print('🔔 [NOTIFICATION] Raw payload: ${response.payload}');
        final data = jsonDecode(response.payload!);
        print('🔔 [NOTIFICATION] Parsed payload data: $data');
        print('🔔 [NOTIFICATION] Payload type: ${data['type']}');

        // Handle incoming call actions
        if (data['type'] == 'incoming_call') {
          print('🔔 [NOTIFICATION] ✅ Incoming call local notification tapped');
          print('🔔 [NOTIFICATION] Call ID from payload: ${data['callId']}');
          print('🔔 [NOTIFICATION] Caller from payload: ${data['callerName']}');
          _handleCallNotificationAction(response.actionId, data);
        }
        // Navigate to chat screen if chat room ID is provided
        else if (data.containsKey('chatRoomId')) {
          print('🔔 [NOTIFICATION] Chat notification tapped');
          print(
            '🔔 [NOTIFICATION] Should navigate to chat room: ${data['chatRoomId']}',
          );
        } else {
          print('🔔 [NOTIFICATION] ❌ Unknown local notification type: ${data['type']}');
        }
      } catch (e) {
        print('🔔 [NOTIFICATION] ❌ Error parsing notification payload: $e');
        print('🔔 [NOTIFICATION] Payload was: ${response.payload}');
      }
    } else {
      print('🔔 [NOTIFICATION] ❌ No payload in notification response');
    }
  }

  // Handle call notification actions
  void _handleCallNotificationAction(String? actionId, Map<String, dynamic> data) {
    print('🔔 [NOTIFICATION] ========== HANDLE CALL ACTION ==========');
    print('🔔 [NOTIFICATION] Action ID: $actionId');
    print('🔔 [NOTIFICATION] Call data: $data');
    
    final callId = data['callId'] as String?;
    print('🔔 [NOTIFICATION] Call ID: $callId');
    
    if (callId == null) {
      print('🔔 [NOTIFICATION] ❌ No call ID found in data, aborting');
      return;
    }

    switch (actionId) {
      case 'answer_call':
        print('🔔 [NOTIFICATION] ✅ Answer call action triggered for call: $callId');
        _navigateToCall(data, shouldAnswer: true);
        break;
      case 'decline_call':
        print('🔔 [NOTIFICATION] ✅ Decline call action triggered for call: $callId');
        _declineCallFromNotification(callId);
        break;
      default:
        print('🔔 [NOTIFICATION] ✅ Default call notification tap - should show call interface: $callId');
        print('🔔 [NOTIFICATION] No specific action, treating as "show call interface"');
        _navigateToCall(data, shouldAnswer: false);
        break;
    }
  }

  // Navigate to call screen when notification is tapped
  void _navigateToCall(Map<String, dynamic> data, {bool shouldAnswer = false}) {
    print('🔔 [NOTIFICATION] ========== NAVIGATE TO CALL ==========');
    print('🔔 [NOTIFICATION] Should answer: $shouldAnswer');
    print('🔔 [NOTIFICATION] Navigator key available: ${navigatorKey != null}');
    
    final context = navigatorKey?.currentContext;
    if (context == null) {
      print('🔔 [NOTIFICATION] ❌ No navigation context available');
      print('🔔 [NOTIFICATION] Navigator key: $navigatorKey');
      print('🔔 [NOTIFICATION] Current context: $context');
      return;
    }

    print('🔔 [NOTIFICATION] ✅ Navigation context available');
    
    // Extract call data
    final callId = data['callId'] as String;
    final callerId = data['callerId'] as String;
    final callerName = data['callerName'] as String;
    final callerAvatar = data['callerAvatar'] as String?;
    final callType = data['callType'] as String;
    final channelName = data['channelName'] as String;
    final timestamp = data['timestamp'] as String?;
    // Extract Agora UIDs from notification data
    final String? callerAgoraUidString = data['callerAgoraUid'] as String?;
    final String? receiverAgoraUidString = data['receiverAgoraUid'] as String?;

    print('🔔 [NOTIFICATION] Call details extracted:');
    print('🔔 [NOTIFICATION] - Call ID: $callId');
    print('🔔 [NOTIFICATION] - Caller ID: $callerId');
    print('🔔 [NOTIFICATION] - Caller Name: $callerName');
    print('🔔 [NOTIFICATION] - Call Type: $callType');
    print('🔔 [NOTIFICATION] - Channel: $channelName');
    print('🔔 [NOTIFICATION] - Caller Agora UID String: $callerAgoraUidString');
    print('🔔 [NOTIFICATION] - Receiver Agora UID String: $receiverAgoraUidString');

    print('🔔 [NOTIFICATION] Triggering incoming call display...');
    // Store call data globally so the IncomingCallListener can pick it up
    _triggerIncomingCallDisplay(callId, callerId, callerName, callerAvatar, callType, channelName, shouldAnswer, receiverAgoraUidString);
  }

  // Trigger incoming call display by updating Firestore
  Future<void> _triggerIncomingCallDisplay(
    String callId,
    String callerId,
    String callerName,
    String? callerAvatar,
    String callType,
    String channelName,
    bool shouldAnswer,
    String? receiverAgoraUidString // Ensured this parameter is present
  ) async {
    print('🔔 [NOTIFICATION] ========== TRIGGER CALL DISPLAY ==========');
    print('🔔 [NOTIFICATION] Call ID: $callId');
    print('🔔 [NOTIFICATION] Should answer: $shouldAnswer');
    
    try {
      print('🔔 [NOTIFICATION] Checking call status in Firestore...');
      // First, check if the call still exists and is active
      final callDoc = await _firestore.collection('calls').doc(callId).get();
      
      if (callDoc.exists) {
        final callData = callDoc.data()!;
        print('🔔 [NOTIFICATION] ✅ Call found in database');
        print('🔔 [NOTIFICATION] Call status: ${callData['status']}');
        print('🔔 [NOTIFICATION] Full call data: $callData');
        
        // Only proceed if call is still ringing
        if (callData['status'] == 'calling') {
          print('🔔 [NOTIFICATION] ✅ Call $callId is still active, processing...');
          
          if (shouldAnswer) {
            print('🔔 [NOTIFICATION] Auto-answering call...');
            // Auto-answer the call
            await _firestore.collection('calls').doc(callId).update({
              'status': 'answered',
            });
            print('🔔 [NOTIFICATION] ✅ Call auto-answered');
          }
          
          // Cancel the notification since we're handling it
          print('🔔 [NOTIFICATION] Cancelling notification...');
          await cancelCallNotification(callId);
          print('🔔 [NOTIFICATION] ✅ Notification cancelled');
          
          print('🔔 [NOTIFICATION] ✅ IncomingCallListener should pick up the call automatically');
          
          // Force show call interface directly since IncomingCallListener might not be working
          print('🔔 [NOTIFICATION] Also attempting direct call interface display...');
          await _showCallInterfaceDirectly(callData, callId, callerId, callerName, callerAvatar, callType, channelName, receiverAgoraUidString); // Ensure receiverAgoraUidString is passed
        } else {
          print('🔔 [NOTIFICATION] ❌ Call $callId is no longer active (status: ${callData['status']})');
          print('🔔 [NOTIFICATION] Cancelling stale notification...');
          await cancelCallNotification(callId);
        }
      } else {
        print('🔔 [NOTIFICATION] ❌ Call $callId not found in database');
        print('🔔 [NOTIFICATION] Cancelling orphaned notification...');
        await cancelCallNotification(callId);
      }
    } catch (e) {
      print('🔔 [NOTIFICATION] ❌ Error handling call notification: $e');
      print('🔔 [NOTIFICATION] Stack trace: ${StackTrace.current}');
    }
  }

  // Method to directly show the call interface using navigatorKey
  // This is called as a fallback if IncomingCallListener might not be working.
  Future<void> _showCallInterfaceDirectly(
    Map<String, dynamic> callData, // Firestore document for the call
    String callId,
    String callerId,
    String callerName,
    String? callerAvatar,
    String callType,
    String channelName,
    String? receiverAgoraUidString // Ensured this parameter is present
  ) async {
    final context = NotificationService.navigatorKey?.currentContext;
    if (context == null) {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: No navigation context available.');
      return;
    }

    print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Attempting to navigate for call $callId.');
    print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Received receiverAgoraUidString from payload: $receiverAgoraUidString');

    int? localAgoraUid;
    if (receiverAgoraUidString != null && receiverAgoraUidString.isNotEmpty) {
      localAgoraUid = int.tryParse(receiverAgoraUidString);
    }

    if (localAgoraUid == null) {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Failed to parse receiverAgoraUidString "$receiverAgoraUidString" from notification payload.');
      // Fallback: Try to get it from the callData map (Firestore doc)
      final dynamic receiverAgoraUidFromDoc = callData['receiverAgoraUid']; // This is the field name we added in CallService
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Attempting fallback to receiverAgoraUid from Firestore doc: $receiverAgoraUidFromDoc');
       if (receiverAgoraUidFromDoc is int) {
        localAgoraUid = receiverAgoraUidFromDoc;
        print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Fallback successful - using int receiverAgoraUid from Firestore doc: $localAgoraUid');
      } else if (receiverAgoraUidFromDoc is String && receiverAgoraUidFromDoc.isNotEmpty) {
        localAgoraUid = int.tryParse(receiverAgoraUidFromDoc);
        print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Fallback successful - using parsed string receiverAgoraUid from Firestore doc: $localAgoraUid');
      }

      if (localAgoraUid == null) {
        print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: receiverAgoraUid (local Agora UID) still not found or invalid after all fallbacks. Cannot navigate.');
        print('🔔 [NOTIFICATION] Firestore callData for $callId: $callData');
        return;
      }
    } else {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Successfully parsed receiverAgoraUidString "$receiverAgoraUidString" from payload to $localAgoraUid.');
    }

    Widget callScreen;

    if (callType == 'audio') {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Navigating to AudioCallScreen for call $callId.');
      callScreen = AudioCallScreen(
        key: ValueKey('audio_$callId'), // Ensures widget rebuilds if callId changes
        channelName: channelName,
        uid: localAgoraUid, // Current user's (receiver's) Agora UID
        callerName: callerName, // Name of the person calling
        callerAvatar: callerAvatar, // Avatar of the person calling
        isIncoming: true, // This is an incoming call notification being handled
        callId: callId, // Pass callId
        launchedFromNotification: true, // Launched from notification
      );
    } else if (callType == 'video') {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Navigating to VideoCallScreen for call $callId.');
      callScreen = VideoCallScreen(
        key: ValueKey('video_$callId'), // Ensures widget rebuilds if callId changes
        channelName: channelName,
        uid: localAgoraUid, // Current user's (receiver's) Agora UID
        callerName: callerName, // Name of the person calling
        callerAvatar: callerAvatar, // Avatar of the person calling
        isIncoming: true, // This is an incoming call notification being handled
        callId: callId, // Pass callId
        launchedFromNotification: true, // Launched from notification
      );
    } else {
      print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Unknown call type "$callType". Cannot navigate.');
      return;
    }

    // Using pushReplacement to avoid stacking call screens if multiple notifications are processed rapidly
    // or if the user is already on a screen that should be replaced by the call.
    // Consider Navigator.push if stacking is desired or appropriate.
    Navigator.pushReplacement(context, MaterialPageRoute(builder: (_) => callScreen));
    print('🔔 [NOTIFICATION] _showCallInterfaceDirectly: Navigation initiated for $callType call.');
  }

  // Decline call from notification
  Future<void> _declineCallFromNotification(String callId) async {
    try {
      await _firestore.collection('calls').doc(callId).update({
        'status': 'declined',
      });
      
      // Cancel the notification
      await cancelCallNotification(callId);
    } catch (e) {
      print('🔔 [NOTIFICATION] Error declining call: $e');
    }
  }

  // Cancel call notification
  Future<void> cancelCallNotification(String callId) async {
    await _localNotifications.cancel(callId.hashCode);
  }

  // Send notification to specific user
  Future<void> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      print('🔔 [NOTIFICATION] Sending notification to user: $userId');

      // Get user's FCM token
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final fcmToken = userDoc.data()?['fcmToken'] as String?;

      if (fcmToken == null) {
        print('🔔 [NOTIFICATION] No FCM token found for user: $userId');
        return;
      }

      // Store notification in Firestore for server-side processing
      // A cloud function or backend service should pick this up and send the actual notification
      await _firestore.collection('notifications').add({
        'userId': userId,
        'fcmToken': fcmToken,
        'title': title,
        'body': body,
        'data': data ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'processed': false,
        'type': 'push_notification',
      });

      print('🔔 [NOTIFICATION] Notification queued for processing');
      print('🔔 [NOTIFICATION] Title: $title');
      print('🔔 [NOTIFICATION] Body: $body');
      print('🔔 [NOTIFICATION] Data: $data');
    } catch (e) {
      print('🔔 [NOTIFICATION] Error sending notification: $e');
    }
  }

  // Update FCM token when it changes
  Future<void> updateFCMToken() async {
    _firebaseMessaging.onTokenRefresh.listen((newToken) async {
      print('🔔 [NOTIFICATION] FCM token refreshed: $newToken');

      final user = _auth.currentUser;
      if (user != null) {
        await _firestore.collection('users').doc(user.uid).set({
          'fcmToken': newToken,
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    });
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(RemoteMessage message) async {
  print('🔔 [NOTIFICATION] Handling background message: ${message.messageId}');
  print('🔔 [NOTIFICATION] Title: ${message.notification?.title}');
  print('🔔 [NOTIFICATION] Body: ${message.notification?.body}');
  print('🔔 [NOTIFICATION] Data: ${message.data}');
}
