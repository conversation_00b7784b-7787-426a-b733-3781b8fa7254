import 'dart:convert';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:http/http.dart' as http;
import 'package:tolk/config/agora_config.dart';

class TokenService {
  /// Generates an Agora token for joining a channel
  /// 
  /// [channelName] - The name of the channel to join
  /// [uid] - User ID (should be unique for each user)
  /// [role] - Either 'publisher' or 'subscriber'
  /// 
  /// Returns the token string or null if generation fails
  static Future<String?> generateToken({
    required String channelName,
    required int uid,
    String role = 'publisher',
  }) async {
    try {
      // For development/testing without token server
      if (AgoraConfig.tokenServerUrl == null || AgoraConfig.tokenServerUrl!.isEmpty) {
        print('⚠️ Warning: Using development mode without token authentication');
        return null; // Return null for development mode
      }

      // Option A: Using Firebase Cloud Functions
      if (!AgoraConfig.tokenServerUrl!.startsWith('http')) {
        return await _generateTokenWithCloudFunction(channelName, uid, role);
      }
      
      // Option B: Using HTTP endpoint
      return await _generateTokenWithHttp(channelName, uid, role);
      
    } catch (e) {
      print('❌ Error generating Agora token: $e');
      return null;
    }
  }

  /// Generate token using Firebase Cloud Functions
  static Future<String?> _generateTokenWithCloudFunction(
    String channelName,
    int uid,
    String role,
  ) async {
    try {
      final callable = FirebaseFunctions.instance.httpsCallable(
        AgoraConfig.tokenServerUrl!,
      );
      
      final result = await callable.call({
        'channelName': channelName,
        'uid': uid,
        'role': role,
      });
      
      if (result.data != null && result.data['token'] != null) {
        print('✅ Token generated successfully via Cloud Function');
        return result.data['token'] as String;
      }
      
      print('❌ Invalid response from Cloud Function');
      return null;
      
    } catch (e) {
      print('❌ Cloud Function error: $e');
      return null;
    }
  }

  /// Generate token using HTTP endpoint
  static Future<String?> _generateTokenWithHttp(
    String channelName,
    int uid,
    String role,
  ) async {
    try {
      final response = await http.post(
        Uri.parse(AgoraConfig.tokenServerUrl!),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'channelName': channelName,
          'uid': uid,
          'role': role,
        }),
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['token'] != null) {
          print('✅ Token generated successfully via HTTP');
          return data['token'] as String;
        }
      }
      
      print('❌ HTTP token generation failed: ${response.statusCode}');
      print('Response: ${response.body}');
      return null;
      
    } catch (e) {
      print('❌ HTTP token generation error: $e');
      return null;
    }
  }

  /// Validates if a token is still valid
  /// This is a basic check - in production you might want to 
  /// decode the JWT token and check expiration
  static bool isTokenValid(String? token) {
    if (token == null || token.isEmpty) {
      return false;
    }
    
    // Basic JWT structure check
    final parts = token.split('.');
    return parts.length == 3;
  }

  /// Generates a unique channel name for calls
  static String generateChannelName(String userId1, String userId2) {
    // Sort user IDs to ensure consistent channel names
    final sortedIds = [userId1, userId2]..sort();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'call_${sortedIds[0]}_${sortedIds[1]}_$timestamp';
  }

  /// Generates a consistent UID from user ID string
  static int generateUid(String userId) {
    // Create a consistent numeric UID from string user ID
    int hash = 0;
    for (int i = 0; i < userId.length; i++) {
      hash = ((hash << 5) - hash + userId.codeUnitAt(i)) & 0x7FFFFFFF;
    }
    // Ensure it's a positive 32-bit integer
    return hash == 0 ? 1 : hash;
  }
}