import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tolk/models/user_model.dart';
// import 'package:tolk/providers/user_provider.dart'; // Not directly used here anymore
import 'package:tolk/services/call_service.dart';
import 'package:tolk/screens/call/audio_call_screen.dart'; // For navigation
import 'package:tolk/screens/call/video_call_screen.dart'; // For navigation
import 'package:tolk/services/token_service.dart'; // For UID generation

class CallHandlerService {
  final CallService _callService = CallService();

  Future<void> makeAudioCall({
    required BuildContext context,
    required UserModel? caller,
    required UserModel? receiver,
  }) async {
    print('📞 [CallHandlerService] makeAudioCall: Initiated. Context mounted: ${context.mounted}');
    if (caller == null || receiver == null) {
      print('📞 [CallHandlerService] makeAudioCall: Caller or Receiver is null. Aborting.');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unable to start call: User details missing')),
        );
      }
      return;
    }
    print('📞 [CallHandlerService] makeAudioCall: Caller: ${caller.uid}, Receiver: ${receiver.uid}');

    final String? callId = await _callService.makeAudioCall(
      caller: caller,
      receiver: receiver,
    );
    print('📞 [CallHandlerService] makeAudioCall: CallService returned callId: $callId. Context mounted: ${context.mounted}');

    if (callId != null && context.mounted) {
      print('📞 [CallHandlerService] makeAudioCall: callId is $callId and context is mounted. Preparing to navigate.');
      final callStatusStream = _callService.listenForCallStatus(callId);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => StreamBuilder<CallData?>(
            stream: callStatusStream,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final callData = snapshot.data!;
                if (callData.status == 'declined' || callData.status == 'ended' || callData.status == 'timeout') {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (context.mounted && Navigator.canPop(context)) {
                      Navigator.of(context).pop();
                    }
                  });
                }
                return AudioCallScreen(
                  channelName: callData.channelName,
                  uid: callData.callerAgoraUid ?? TokenService.generateUid(caller.uid), // Use UID from CallData if available
                  callerName: receiver.name ?? 'Unknown',
                  callerAvatar: receiver.profilePicture,
                  isIncoming: false,
                  callId: callId,
                );
              }
              return Scaffold(
                backgroundColor: Colors.black,
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(color: Colors.white),
                      const SizedBox(height: 20),
                      Text(
                        'Calling ${receiver.name ?? 'Unknown'}...',
                        style: const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
      print('📞 [CallHandlerService] makeAudioCall: Navigation initiated.');
    } else if (callId == null && context.mounted) {
      print('📞 [CallHandlerService] makeAudioCall: callId is null but context is mounted. Showing SnackBar.');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to initiate audio call.')),
      );
    } else {
      print('📞 [CallHandlerService] makeAudioCall: Did not navigate. callId: $callId, context.mounted: ${context.mounted}');
    }
  }

  Future<void> makeVideoCall({
    required BuildContext context,
    required UserModel? caller,
    required UserModel? receiver,
  }) async {
    print('📞 [CallHandlerService] makeVideoCall: Initiated. Context mounted: ${context.mounted}');
    if (caller == null || receiver == null) {
      print('📞 [CallHandlerService] makeVideoCall: Caller or Receiver is null. Aborting.');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unable to start call: User details missing')),
        );
      }
      return;
    }
    print('📞 [CallHandlerService] makeVideoCall: Caller: ${caller.uid}, Receiver: ${receiver.uid}');

    final String? callId = await _callService.makeVideoCall(
      caller: caller,
      receiver: receiver,
    );
    print('📞 [CallHandlerService] makeVideoCall: CallService returned callId: $callId. Context mounted: ${context.mounted}');

    if (callId != null && context.mounted) {
      print('📞 [CallHandlerService] makeVideoCall: callId is $callId and context is mounted. Preparing to navigate.');
      final callStatusStream = _callService.listenForCallStatus(callId);
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => StreamBuilder<CallData?>(
            stream: callStatusStream,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final callData = snapshot.data!;
                if (callData.status == 'declined' || callData.status == 'ended' || callData.status == 'timeout') {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (context.mounted && Navigator.canPop(context)) {
                      Navigator.of(context).pop();
                    }
                  });
                }
                return VideoCallScreen(
                  channelName: callData.channelName,
                  uid: callData.callerAgoraUid ?? TokenService.generateUid(caller.uid), // Use UID from CallData if available
                  callerName: receiver.name ?? 'Unknown',
                  callerAvatar: receiver.profilePicture,
                  isIncoming: false,
                  callId: callId,
                );
              }
              return Scaffold(
                backgroundColor: Colors.black,
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(color: Colors.white),
                      const SizedBox(height: 20),
                      Text(
                        'Calling ${receiver.name ?? 'Unknown'} for video...',
                        style: const TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
      print('📞 [CallHandlerService] makeVideoCall: Navigation initiated.');
    } else if (callId == null && context.mounted) {
      print('📞 [CallHandlerService] makeVideoCall: callId is null but context is mounted. Showing SnackBar.');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to initiate video call.')),
      );
    } else {
      print('📞 [CallHandlerService] makeVideoCall: Did not navigate. callId: $callId, context.mounted: ${context.mounted}');
    }
  }
}