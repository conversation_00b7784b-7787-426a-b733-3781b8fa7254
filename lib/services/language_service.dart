import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:tolk/models/language_model.dart';

class LanguageService {
  static LanguageData? _languageData;
  static List<Language>? _translationLanguages;

  /// Load languages from the JSON file
  static Future<LanguageData> loadLanguages() async {
    if (_languageData != null) {
      return _languageData!;
    }

    try {
      final String jsonString = await rootBundle.loadString('assets/json/lang.json');
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      _languageData = LanguageData.fromJson(jsonData);
      return _languageData!;
    } catch (e) {
      print('Error loading languages: $e');
      // Return empty data if loading fails
      return const LanguageData(textLanguages: [], ttsLanguages: []);
    }
  }

  /// Get all translation languages (text languages)
  static Future<List<Language>> getTranslationLanguages() async {
    if (_translationLanguages != null) {
      return _translationLanguages!;
    }

    final languageData = await loadLanguages();
    _translationLanguages = languageData.textLanguages;
    return _translationLanguages!;
  }

  /// Get language by code
  static Future<Language?> getLanguageByCode(String code) async {
    final languages = await getTranslationLanguages();
    try {
      return languages.firstWhere((lang) => lang.code == code);
    } catch (e) {
      return null;
    }
  }

  /// Get popular languages (commonly used ones)
  static Future<List<Language>> getPopularLanguages() async {
    final languages = await getTranslationLanguages();
    
    // List of popular language codes
    const popularCodes = [
      'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh-CN',
      'ar', 'hi', 'tr', 'pl', 'nl', 'sv', 'da', 'no', 'fi'
    ];

    final popularLanguages = <Language>[];
    for (final code in popularCodes) {
      final language = languages.where((lang) => lang.code == code).firstOrNull;
      if (language != null) {
        popularLanguages.add(language);
      }
    }

    return popularLanguages;
  }

  /// Search languages by name
  static Future<List<Language>> searchLanguages(String query) async {
    if (query.isEmpty) {
      return await getTranslationLanguages();
    }

    final languages = await getTranslationLanguages();
    final lowercaseQuery = query.toLowerCase();

    return languages.where((language) =>
      language.name.toLowerCase().contains(lowercaseQuery) ||
      language.code.toLowerCase().contains(lowercaseQuery)
    ).toList();
  }

  /// Get default language (English)
  static Future<Language> getDefaultLanguage() async {
    final language = await getLanguageByCode('en');
    return language ?? const Language(name: 'English', code: 'en');
  }

  /// Clear cached data (useful for testing or memory management)
  static void clearCache() {
    _languageData = null;
    _translationLanguages = null;
  }
}
