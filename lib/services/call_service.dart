import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:tolk/models/user_model.dart';
import 'package:tolk/screens/call/audio_call_screen.dart';
import 'package:tolk/screens/call/video_call_screen.dart';
import 'package:tolk/services/token_service.dart';
import 'package:tolk/services/notification_service.dart';
import 'package:tolk/services/chat_service.dart'; // Added ChatService import
import 'package:tolk/models/chat_models.dart'; // Import MessageType
import 'dart:math';
import 'dart:async'; // For Timer

enum CallType { audio, video }

class CallData {
  final String callId;
  final String callerId;
  final String callerName;
  final String? callerAvatar;
  final String receiverId;
  final CallType type;
  final String channelName;
  final DateTime timestamp; // Represents when the call was initiated
  final String status; // 'calling', 'answered', 'ended', 'declined', 'timeout'
  final DateTime? callStartTime; // When the call was answered
  final DateTime? callEndTime; // When the call was ended
  final int? callerAgoraUid; // Agora UID for the caller
  final int? receiverAgoraUid; // Agora UID for the receiver
  final String? chatRoomId; // ID of the chat room where the call message was sent
  final String? chatMessageId; // ID of the call message in the chat

  CallData({
    required this.callId,
    required this.callerId,
    required this.callerName,
    this.callerAvatar,
    required this.receiverId,
    required this.type,
    required this.channelName,
    required this.timestamp,
    required this.status,
    this.callStartTime,
    this.callEndTime,
    this.callerAgoraUid,
    this.receiverAgoraUid,
    this.chatRoomId,
    this.chatMessageId,
  });

  factory CallData.fromMap(Map<String, dynamic> map) {
    return CallData(
      callId: map['callId'] ?? '',
      callerId: map['callerId'] ?? '',
      callerName: map['callerName'] ?? '',
      callerAvatar: map['callerAvatar'],
      receiverId: map['receiverId'] ?? '',
      type: CallType.values.firstWhere(
        (e) => e.toString() == 'CallType.${map['type']}',
        orElse: () => CallType.audio,
      ),
      channelName: map['channelName'] ?? '',
      timestamp: (map['timestamp'] as Timestamp).toDate(),
      status: map['status'] ?? 'calling',
      callStartTime: map['callStartTime'] != null ? (map['callStartTime'] as Timestamp).toDate() : null,
      callEndTime: map['callEndTime'] != null ? (map['callEndTime'] as Timestamp).toDate() : null,
      callerAgoraUid: map['callerAgoraUid'],
      receiverAgoraUid: map['receiverAgoraUid'],
      chatRoomId: map['chatRoomId'],
      chatMessageId: map['chatMessageId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'callId': callId,
      'callerId': callerId,
      'callerName': callerName,
      'callerAvatar': callerAvatar,
      'receiverId': receiverId,
      'type': type.toString().split('.').last,
      'channelName': channelName,
      'timestamp': Timestamp.fromDate(timestamp),
      'status': status,
      'callStartTime': callStartTime != null ? Timestamp.fromDate(callStartTime!) : null,
      'callEndTime': callEndTime != null ? Timestamp.fromDate(callEndTime!) : null,
      'callerAgoraUid': callerAgoraUid,
      'receiverAgoraUid': receiverAgoraUid,
      'chatRoomId': chatRoomId,
      'chatMessageId': chatMessageId,
    };
  }
}

class CallService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final ChatService _chatService = ChatService(); // Added ChatService instance
  static const String _callsCollection = 'calls';
  final Map<String, Timer> _callTimeoutTimers = {};
  final Set<String> _locallyActiveCallIds = {}; // Tracks calls active in the UI

  // Generate a unique channel name
  String _generateChannelName() {
    return TokenService.generateChannelName(
      DateTime.now().millisecondsSinceEpoch.toString(),
      Random().nextInt(10000).toString(),
    );
  }

  // Generate a unique call ID
  String _generateCallId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(10000);
    return 'call_${timestamp}_$randomNum';
  }

  // Initiate a call
  Future<String> initiateCall({
    required String callerId,
    required String callerName,
    String? callerAvatar,
    required String receiverId,
    required CallType type,
    String? chatRoomId, // Added
    String? chatMessageId, // Added
  }) async {
    print('📞 [CALL_SERVICE] ========== INITIATING CALL ==========');
    print('📞 [CALL_SERVICE] Caller ID: $callerId');
    print('📞 [CALL_SERVICE] Caller Name: $callerName');
    print('📞 [CALL_SERVICE] Receiver ID: $receiverId');
    print('📞 [CALL_SERVICE] Call Type: $type');
    
    final callId = _generateCallId();
    final channelName = _generateChannelName();
    final callerAgoraUid = TokenService.generateUid(callerId); // Generate Agora UID for caller
    final receiverAgoraUid = TokenService.generateUid(receiverId); // Generate Agora UID for receiver

    print('📞 [CALL_SERVICE] Generated Call ID: $callId');
    print('📞 [CALL_SERVICE] Generated Channel: $channelName');
    print('📞 [CALL_SERVICE] Generated Caller Agora UID: $callerAgoraUid');
    print('📞 [CALL_SERVICE] Generated Receiver Agora UID: $receiverAgoraUid');

    final callData = CallData(
      callId: callId,
      callerId: callerId,
      callerName: callerName,
      callerAvatar: callerAvatar,
      receiverId: receiverId,
      type: type,
      channelName: channelName,
      timestamp: DateTime.now(),
      status: 'calling',
      callerAgoraUid: callerAgoraUid,
      receiverAgoraUid: receiverAgoraUid,
      chatRoomId: chatRoomId, // Added
      chatMessageId: chatMessageId, // Added
    );

    print('📞 [CALL_SERVICE] Call data created: ${callData.toMap()}');

    // Save call data to Firestore
    print('📞 [CALL_SERVICE] Saving call to Firestore...');
    await _firestore.collection(_callsCollection).doc(callId).set(callData.toMap());
    print('📞 [CALL_SERVICE] ✅ Call saved to Firestore');

    // Send notification to receiver
    print('📞 [CALL_SERVICE] Sending notification to receiver...');
    await _sendCallNotification(callData);
    print('📞 [CALL_SERVICE] ✅ Notification sent');

    // Start call timeout timer (30 seconds)
    print('📞 [CALL_SERVICE] Starting timeout timer...');
    _initiateCallTimeout(callId); // Changed from _startCallTimeout
    print('📞 [CALL_SERVICE] ✅ Timeout timer initiated');

    print('📞 [CALL_SERVICE] ✅ Call initiation complete: $callId');
    return callId;
  }

  // Initiate and manage call timeout timer
  void _initiateCallTimeout(String callId) {
    // Cancel any existing timer for this callId, just in case
    _cancelCallTimeout(callId);

    _callTimeoutTimers[callId] = Timer(const Duration(seconds: 30), () async {


      
      print('📞 [CALL_SERVICE] Timeout timer fired for call $callId');
      _callTimeoutTimers.remove(callId); // Remove timer from map as it has fired

      // Check if the call is marked as locally active (UI timer running on caller's device)
      if (_locallyActiveCallIds.contains(callId)) {
        print('📞 [CALL_SERVICE] Call $callId is locally active. Timeout action aborted by local state.');
        // The call screen is responsible for calling setCallLocallyInactive when the call actually ends.
        return;
      }

      // If not locally active, proceed with Firestore-based timeout logic
      print('📞 [CALL_SERVICE] Call $callId is not locally active. Proceeding with Firestore timeout check.');
      try {
        DocumentSnapshot callDoc = await _firestore.collection(_callsCollection).doc(callId).get();
        
        if (callDoc.exists) {
          final callDataInstance = CallData.fromMap(callDoc.data()! as Map<String, dynamic>);
          
          // Proceed with timeout only if callStartTime is not set in Firestore.
          if (callDataInstance.callStartTime == null) {
            print('📞 [CALL_SERVICE] Call $callId: callStartTime is null in Firestore. Current FS status: "${callDataInstance.status}". Proceeding with timeout.');
            
            await _firestore.collection(_callsCollection).doc(callId).update({
              'status': 'timeout',
            });
            await _cancelCallNotification(callId);
            setCallLocallyInactive(callId); // Clean up local state as timeout is happening
            print('📞 Call $callId timed out (not locally active and callStartTime was null in FS).');

            // Update chat message metadata
            if (callDataInstance.chatRoomId != null && callDataInstance.chatMessageId != null) {
              await _updateChatMessageMetadata(
                chatRoomId: callDataInstance.chatRoomId!,
                messageId: callDataInstance.chatMessageId!,
                newMetadata: {'call_status_detail': 'No answer'}, // Or 'Missed call'
              );
            }

            if (callDataInstance.status == 'calling') {
                // System message for missed call (timeout) was here, now skipped.
                // We still might want to log or handle this case if needed in the future.
                print('📞 [CALL_SERVICE] Call $callId (was "calling") timed out. Chat message metadata updated.');
            } else {
                print('📞 [CALL_SERVICE] Call $callId timed out (not locally active, callStartTime null in FS), but FS status was already "${callDataInstance.status}". Chat message metadata updated.');
            }
          } else {
            // This case means: not locally active, but Firestore says it was answered.
            print('📞 [CALL_SERVICE] Call $callId: Not locally active, but callStartTime is set in Firestore (${callDataInstance.callStartTime}). Timeout action aborted by Firestore state. FS Status: ${callDataInstance.status}');
            setCallLocallyInactive(callId); // Clean up local state
          }
        } else {
          print('📞 [CALL_SERVICE] Call document $callId not found during timeout check (after not locally active).');
          setCallLocallyInactive(callId); // Clean up local state
        }
      } catch (e) {
        print('❌ Error handling call timeout for $callId (after not locally active): $e');
        setCallLocallyInactive(callId); // Clean up local state in case of error
      }
    });
    print('📞 [CALL_SERVICE] Timeout timer scheduled for call $callId');
  }

  void _cancelCallTimeout(String callId) {
    if (_callTimeoutTimers.containsKey(callId)) {
      _callTimeoutTimers[callId]?.cancel();
      _callTimeoutTimers.remove(callId);
      print('📞 [CALL_SERVICE] Timeout timer cancelled for call $callId');
    }
  }

  // Methods to be called by UI to update local call state
  void setCallLocallyActive(String callId) {
    print('📞 [CALL_SERVICE] Setting call $callId as locally active.');
    _locallyActiveCallIds.add(callId);
  }

  void setCallLocallyInactive(String callId) {
    print('📞 [CALL_SERVICE] Setting call $callId as locally inactive.');
    _locallyActiveCallIds.remove(callId);
  }
  
  // Send call notification to receiver
  Future<void> _sendCallNotification(CallData callData) async {
    print('📞 [CALL_SERVICE] ========== SENDING NOTIFICATION ==========');
    print('📞 [CALL_SERVICE] Receiver ID: ${callData.receiverId}');
    print('📞 [CALL_SERVICE] Call ID: ${callData.callId}');
    print('📞 [CALL_SERVICE] Caller: ${callData.callerName}');
    
    try {
      final notificationService = NotificationService();
      
      final callTypeText = callData.type == CallType.audio ? 'Audio' : 'Video';
      print('📞 [CALL_SERVICE] Call type: $callTypeText');
      
      final notificationData = {
        'type': 'incoming_call',
        'callId': callData.callId,
        'callerId': callData.callerId,
        'callerName': callData.callerName,
        'callerAvatar': callData.callerAvatar ?? '',
        'callType': callData.type.toString().split('.').last,
        'channelName': callData.channelName,
        'timestamp': callData.timestamp.millisecondsSinceEpoch.toString(),
        // Add Agora UIDs to notification payload
        'callerAgoraUid': callData.callerAgoraUid.toString(),
        'receiverAgoraUid': callData.receiverAgoraUid.toString(),
      };
      
      print('📞 [CALL_SERVICE] Notification data: $notificationData');
      
      await notificationService.sendNotificationToUser(
        userId: callData.receiverId,
        title: 'Incoming $callTypeText Call',
        body: '${callData.callerName} is calling you',
        data: notificationData,
      );
      
      print('📞 [CALL_SERVICE] ✅ Notification sent successfully');
    } catch (e) {
      print('📞 [CALL_SERVICE] ❌ Error sending call notification: $e');
      print('📞 [CALL_SERVICE] Stack trace: ${StackTrace.current}');
    }
  }

  // Answer a call
  Future<void> answerCall(String callId) async {
    print('📞 [CALL_SERVICE] ========== ANSWERING CALL ==========');
    print('📞 [CALL_SERVICE] Call ID: $callId');

    _cancelCallTimeout(callId); // Cancel the 30s timeout timer.
    // The UI (caller or receiver) will call setCallLocallyActive when their timer starts.
    // If this is the receiver answering, their UI will set it.
    // If this is the caller's device reacting to an "answered" status from Firestore,
    // the caller's UI should have already set it when its timer started.
    
    await _firestore.collection(_callsCollection).doc(callId).update({
      'status': 'answered',
      'callStartTime': FieldValue.serverTimestamp(), // Record call start time
    });
    
    print('📞 [CALL_SERVICE] ✅ Call status updated to answered and startTime recorded for $callId');
    
    // Cancel any notification for this call
    print('📞 [CALL_SERVICE] Cancelling notification for answered call $callId...');
    await _cancelCallNotification(callId);
    print('📞 [CALL_SERVICE] ✅ Answer call complete for $callId');
  }

  // Decline a call
  Future<void> declineCall(String callId) async {
    print('📞 [CALL_SERVICE] ========== DECLINING CALL ==========');
    print('📞 [CALL_SERVICE] Call ID: $callId');

    _cancelCallTimeout(callId);
    setCallLocallyInactive(callId); // Call is declined, so it's no longer locally active on this device.

    final callDocSnapshot = await _firestore.collection(_callsCollection).doc(callId).get();
    if (!callDocSnapshot.exists) {
      print('📞 [CALL_SERVICE] ❌ Call document $callId not found for declining.');
      return;
    }
    final originalCallData = CallData.fromMap(callDocSnapshot.data()!);

    // Update status first
    await _firestore.collection(_callsCollection).doc(callId).update({
      'status': 'declined',
    });
    print('📞 [CALL_SERVICE] ✅ Call status updated to declined for $callId');

    // Update chat message metadata
    if (originalCallData.chatRoomId != null && originalCallData.chatMessageId != null) {
      // Determine who declined to set the message appropriately
      // This 'declineCall' is typically invoked by the receiver.
      // The message in the chat should reflect that the *other party* (caller) sees "Declined"
      // and the *receiver* (who pressed decline) sees "Call declined".
      // For simplicity, we'll use a generic "Declined" for now, or differentiate if needed.
      // Let's assume the message is for the caller in this context.
      // If the original message was sent by the caller, update it to show "Declined by [ReceiverName]"
      // If the original message was sent by the receiver (not typical for call initiation), adjust logic.

      // For the message in the chat (which is usually from the caller's perspective initially):
      await _updateChatMessageMetadata(
        chatRoomId: originalCallData.chatRoomId!,
        messageId: originalCallData.chatMessageId!,
        newMetadata: {'call_status_detail': 'Declined'},
      );
    }

    // Cancel any local notification
    await _cancelCallNotification(callId);
    print('📞 [CALL_SERVICE] ✅ Notification cancelled for declined call $callId.');

    // System messages for "You declined" and "[Other] declined"
    if (originalCallData.status == 'calling') {
        try {
          // Message for the receiver (who pressed decline)
          final chatRoomIdForReceiver = await _chatService.createOrGetChatRoom(originalCallData.callerId);
          if (chatRoomIdForReceiver.isNotEmpty) {
            await _chatService.sendSystemMessage(
              chatRoomId: chatRoomIdForReceiver,
              text: 'You declined the call from ${originalCallData.callerName}.',
              receiverId: originalCallData.receiverId,
            );
            print('📞 [CALL_SERVICE] Sent "You declined call" system message to receiver ${originalCallData.receiverId} for call $callId');
          }

          // Message for the caller
          final chatRoomIdForCaller = await _chatService.createOrGetChatRoom(originalCallData.receiverId);
          if (chatRoomIdForCaller.isNotEmpty) {
            final receiverUserDoc = await _firestore.collection('users').doc(originalCallData.receiverId).get();
            String receiverDisplayName = receiverUserDoc.exists ? (receiverUserDoc.data()!['name'] ?? originalCallData.receiverId) : originalCallData.receiverId;
            await _chatService.sendSystemMessage(
              chatRoomId: chatRoomIdForCaller,
              text: '$receiverDisplayName declined your call.',
              receiverId: originalCallData.callerId,
            );
            print('📞 [CALL_SERVICE] Sent "$receiverDisplayName declined your call" system message to caller ${originalCallData.callerId} for call $callId');
          }
        } catch (e) {
          print('📞 [CALL_SERVICE] Error sending system messages for declined call $callId: $e');
        }
    } else {
      print('📞 [CALL_SERVICE] Call $callId was not in "calling" state (was ${originalCallData.status}), no specific decline system message sent from CallService.declineCall.');
    }
    print('📞 [CALL_SERVICE] ✅ Decline call processing complete for $callId');
  }


  // End a call (e.g., user hangs up during an active call, or caller cancels before answered)
  Future<void> endCall(String callId) async {
    print('📞 [CALL_SERVICE] ========== ENDING CALL ==========');
    print('📞 [CALL_SERVICE] Call ID: $callId');
    setCallLocallyInactive(callId); // Call is ending, so it's no longer locally active
    _cancelCallTimeout(callId); // Also ensure the 30s timeout timer is cancelled

    final callDocSnapshot = await _firestore.collection(_callsCollection).doc(callId).get();
    if (!callDocSnapshot.exists) {
      print('📞 [CALL_SERVICE] ❌ Call document $callId not found for ending.');
      return;
    }
    final originalCallData = CallData.fromMap(callDocSnapshot.data()!);
    final now = FieldValue.serverTimestamp();

    Map<String, dynamic> updateData = {'status': 'ended'};

    if (originalCallData.status == 'answered') {
      updateData['callEndTime'] = now;
    }

    // Update status and potentially callEndTime
    await _firestore.collection(_callsCollection).doc(callId).update(updateData);
    // The 'status' field in updateData is the primary indicator. 'callEndTime' is only for answered calls.
    print('📞 [CALL_SERVICE] ✅ Call status updated to "${updateData['status']}" for $callId. CallEndTime set: ${updateData.containsKey('callEndTime')}');
    
    // Cancel any local notification
    await _cancelCallNotification(callId);
    print('📞 [CALL_SERVICE] ✅ Notification cancelled for ended call $callId.');

    // Fetch the updated document to get server timestamps if possible, or use original if not critical
    final updatedCallDoc = await _firestore.collection(_callsCollection).doc(callId).get();
    final finalCallData = updatedCallDoc.exists ? CallData.fromMap(updatedCallDoc.data()!) : originalCallData;


    // Scenario 1: Call was 'calling' and is now 'ended' (caller cancelled ringing call)
    if (originalCallData.status == 'calling') {
      if (finalCallData.chatRoomId != null && finalCallData.chatMessageId != null) {
        await _updateChatMessageMetadata(
          chatRoomId: finalCallData.chatRoomId!,
          messageId: finalCallData.chatMessageId!,
          newMetadata: {'call_status_detail': 'Cancelled'}, // Or 'No answer'
        );
      }
      // System message for missed call (caller ended ringing) was here, now skipped.
      print('📞 [CALL_SERVICE] Caller cancelled ringing call $callId. Chat message metadata updated.');
    }
    // Scenario 2: Call was 'answered' and is now 'ended'
    else if (originalCallData.status == 'answered') {

      String durationForMetadata = "Ended";
      if (finalCallData.callStartTime != null && finalCallData.callEndTime != null) {
        final duration = finalCallData.callEndTime!.difference(finalCallData.callStartTime!);
        final minutes = duration.inMinutes;
        final seconds = duration.inSeconds % 60;
        durationForMetadata = 'Duration: ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      }

      if (finalCallData.chatRoomId != null && finalCallData.chatMessageId != null) {
        await _updateChatMessageMetadata(
          chatRoomId: finalCallData.chatRoomId!,
          messageId: finalCallData.chatMessageId!,
          newMetadata: {
            'call_status_detail': 'Call ended',
            'call_duration': durationForMetadata,
          },
        );
      }
      
      // try {
      //   // Send system messages with duration to both parties
      //   final chatRoomIdForCaller = await _chatService.createOrGetChatRoom(finalCallData.receiverId);
      //   if (chatRoomIdForCaller.isNotEmpty) {
      //     await _chatService.sendSystemMessage(
      //       chatRoomId: chatRoomIdForCaller,
      //       text: durationMessage,
      //       receiverId: finalCallData.callerId,
      //     );
      //      print('📞 [CALL_SERVICE] Sent "$durationMessage" system message to caller ${finalCallData.callerId} for call $callId');
      //   }

      //   final chatRoomIdForReceiver = await _chatService.createOrGetChatRoom(finalCallData.callerId);
      //   if (chatRoomIdForReceiver.isNotEmpty) {
      //     await _chatService.sendSystemMessage(
      //       chatRoomId: chatRoomIdForReceiver,
      //       text: durationMessage,
      //       receiverId: finalCallData.receiverId,
      //     );
      //     print('📞 [CALL_SERVICE] Sent "$durationMessage" system message to receiver ${finalCallData.receiverId} for call $callId');
      //   }
      // } catch (e) {
      //   print('📞 [CALL_SERVICE] Error sending call ended with duration system message for call $callId: $e');
      // }
    }
    // Other statuses (e.g., ending an already declined/timed-out call) might not need new system messages here.

    print('📞 [CALL_SERVICE] ✅ End call processing complete for $callId');
  }

 Future<void> _updateChatMessageMetadata({
    required String chatRoomId,
    required String messageId,
    required Map<String, dynamic> newMetadata,
  }) async {
    try {
      print('📞 [CALL_SERVICE] Updating chat message metadata for $messageId: $newMetadata');
      // Messages are in a top-level 'messages' collection
      await _firestore
          .collection('messages')
          .doc(messageId)
          .update({'metadata': newMetadata});
      print('📞 [CALL_SERVICE] ✅ Chat message metadata updated successfully for message $messageId.');
    } catch (e) {
      print('📞 [CALL_SERVICE] ❌ Error updating chat message metadata for $messageId: $e');
    }
  }

  // Cancel call notification
  Future<void> _cancelCallNotification(String callId) async {
    try {
      final notificationService = NotificationService();
      await notificationService.cancelCallNotification(callId);
    } catch (e) {
      print('❌ Error canceling call notification for $callId: $e');
    }
  }

  // Listen for incoming calls
  Stream<CallData?> listenForIncomingCalls(String userId) {
    print('📞 [CALL_SERVICE] ========== LISTENING FOR CALLS ==========');
    print('📞 [CALL_SERVICE] User ID: $userId');
    print('📞 [CALL_SERVICE] Collection: $_callsCollection');
    
    return _firestore
        .collection(_callsCollection)
        .where('receiverId', isEqualTo: userId)
        .where('status', isEqualTo: 'calling')
        .orderBy('timestamp', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
      // print('📞 [CALL_SERVICE] ========== FIRESTORE SNAPSHOT (Incoming Calls) ==========');
      // print('📞 [CALL_SERVICE] Snapshot received for user: $userId');
      // print('📞 [CALL_SERVICE] Docs count: ${snapshot.docs.length}');
      
      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        final data = doc.data();
        // print('📞 [CALL_SERVICE] ✅ Incoming call found: ${doc.id}');
        
        final callData = CallData.fromMap(data);
        // print('📞 [CALL_SERVICE] - Parsed Call ID: ${callData.callId}, Status: ${callData.status}');
        return callData;
      } else {
        // print('📞 [CALL_SERVICE] ❌ No incoming calls found for user: $userId');
        return null;
      }
    });
  }

  // Listen for call status changes
  Stream<CallData?> listenForCallStatus(String callId) {
    return _firestore
        .collection(_callsCollection)
        .doc(callId)
        .snapshots()
        .map((snapshot) {
      if (snapshot.exists) {
        // print('📞 [CALL_SERVICE] Status update for call $callId: ${snapshot.data()?['status']}');
        return CallData.fromMap(snapshot.data()!);
      }
      // print('📞 [CALL_SERVICE] Call $callId document does not exist or no longer exists.');
      return null;
    });
  }

  // Make an audio call
  Future<String?> makeAudioCall({
    // removed BuildContext context,
    required UserModel caller,
    required UserModel receiver,
  }) async {
    String? chatRoomId;
    String? chatMessageId;
    try {
      // First, create/get chat room and send the initial message
      chatRoomId = await _chatService.createOrGetChatRoom(receiver.uid);
      if (chatRoomId.isNotEmpty) {
        chatMessageId = await _chatService.sendMessage(
          chatRoomId: chatRoomId,
          text: 'Audio Call', // This is the main text for the CallMessageWidget
          type: MessageType.audioCall,
          metadata: {'call_status_detail': 'Calling...'}, // Initial status
        );
        if (chatMessageId != null) {
          print('📞 [CALL_SERVICE] Sent initial audio call message ($chatMessageId) to chat room $chatRoomId');
        } else {
          print('📞 [CALL_SERVICE] Failed to send initial audio call message to chat room $chatRoomId.');
        }
      } else {
        print('📞 [CALL_SERVICE] Could not obtain chatRoomId for audio call message.');
      }

      final callId = await initiateCall(
        callerId: caller.uid,
        callerName: caller.name ?? 'Unknown',
        callerAvatar: caller.profilePicture,
        receiverId: receiver.uid,
        type: CallType.audio,
        chatRoomId: chatRoomId, // Pass to initiateCall
        chatMessageId: chatMessageId, // Pass to initiateCall
      );
      
      return callId; // Return callId
    } catch (e) {
      print('❌ Failed to start audio call: $e');
      // Optionally, rethrow or handle more gracefully if context were available for SnackBar
      return null; // Indicate failure
    }
  }

  // Make a video call
  Future<String?> makeVideoCall({
    // removed BuildContext context,
    required UserModel caller,
    required UserModel receiver,
  }) async {
    String? chatRoomId;
    String? chatMessageId;
    try {
      // First, create/get chat room and send the initial message
      chatRoomId = await _chatService.createOrGetChatRoom(receiver.uid);
      if (chatRoomId.isNotEmpty) {
        chatMessageId = await _chatService.sendMessage(
          chatRoomId: chatRoomId,
          text: 'Video Call', // This is the main text for the CallMessageWidget
          type: MessageType.videoCall,
          metadata: {'call_status_detail': 'Calling...'}, // Initial status
        );
        if (chatMessageId != null) {
          print('📞 [CALL_SERVICE] Sent initial video call message ($chatMessageId) to chat room $chatRoomId');
        } else {
          print('📞 [CALL_SERVICE] Failed to send initial video call message to chat room $chatRoomId.');
        }
      } else {
        print('📞 [CALL_SERVICE] Could not obtain chatRoomId for video call message.');
      }

      final callId = await initiateCall(
        callerId: caller.uid,
        callerName: caller.name ?? 'Unknown',
        callerAvatar: caller.profilePicture,
        receiverId: receiver.uid,
        type: CallType.video,
        chatRoomId: chatRoomId, // Pass to initiateCall
        chatMessageId: chatMessageId, // Pass to initiateCall
      );

      return callId; // Return callId
    } catch (e) {
      print('❌ Failed to start video call: $e');
      // Optionally, rethrow or handle more gracefully if context were available for SnackBar
      return null; // Indicate failure
    }
  }

  // This method might not be directly used if ChatListScreen handles navigation
  // upon accepting a call. However, it's good to keep for other potential uses.
  void handleIncomingCallNavigation({
    required BuildContext context,
    required CallData callData,
    required UserModel currentUser, // The user who is receiving the call
    bool launchedFromNotification = false, // Added parameter with default
  }) {
    final String currentUserId = currentUser.uid;
    // The UID for Agora should be the one assigned to the receiver in the CallData document
    final int uid = callData.receiverAgoraUid ?? TokenService.generateUid(currentUserId);
    print('📞 [CALL_SERVICE] handleIncomingCallNavigation: Using Agora UID: $uid for receiver ${currentUser.uid}');
    print('📞 [CALL_SERVICE] handleIncomingCallNavigation: CallData receiverAgoraUid was: ${callData.receiverAgoraUid}');


    Widget callScreen;
    if (callData.type == CallType.audio) {
      callScreen = AudioCallScreen(
        channelName: callData.channelName,
        uid: uid,
        callerName: callData.callerName, // Name of the person calling
        callerAvatar: callData.callerAvatar,
        isIncoming: true,
        callId: callData.callId,
        launchedFromNotification: launchedFromNotification, // Pass the flag
      );
    } else {
      callScreen = VideoCallScreen(
        channelName: callData.channelName,
        uid: uid,
        callerName: callData.callerName, // Name of the person calling
        callerAvatar: callData.callerAvatar,
        isIncoming: true,
        callId: callData.callId,
        launchedFromNotification: launchedFromNotification, // Pass the flag
      );
    }

    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => callScreen),
    );
  }
}