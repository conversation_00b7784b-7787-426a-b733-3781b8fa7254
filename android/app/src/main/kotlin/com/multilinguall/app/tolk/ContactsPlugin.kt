package com.multilinguall.app.tolk

import android.content.Context
import android.provider.ContactsContract
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class ContactsPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "tolk/contacts")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getContacts" -> {
                try {
                    val contacts = getDeviceContacts()
                    result.success(contacts)
                } catch (e: Exception) {
                    result.error("CONTACT_ERROR", "Failed to get contacts: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun getDeviceContacts(): List<Map<String, Any>> {
        val contacts = mutableListOf<Map<String, Any>>()

        val cursor = context.contentResolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            arrayOf(
                ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
                ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
                ContactsContract.CommonDataKinds.Phone.NUMBER
            ),
            null,
            null,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
        )

        cursor?.use {
            val contactIdIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
            val nameIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
            val phoneIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)

            val contactMap = mutableMapOf<String, MutableMap<String, Any>>()

            while (it.moveToNext()) {
                val contactId = it.getString(contactIdIndex) ?: continue
                val name = it.getString(nameIndex) ?: ""
                val phone = it.getString(phoneIndex) ?: ""

                if (phone.isNotEmpty()) {
                    if (contactMap.containsKey(contactId)) {
                        // Add phone to existing contact
                        val existingContact = contactMap[contactId]!!
                        val phones = existingContact["phones"] as MutableList<String>
                        if (!phones.contains(phone)) {
                            phones.add(phone)
                        }
                    } else {
                        // Create new contact
                        contactMap[contactId] = mutableMapOf(
                            "id" to contactId,
                            "displayName" to name,
                            "phones" to mutableListOf(phone)
                        )
                    }
                }
            }

            contacts.addAll(contactMap.values)
        }

        return contacts
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}
