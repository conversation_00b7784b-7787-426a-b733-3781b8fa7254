# 🐛 Incoming Call Debug Guide

## Issue Reported
User is getting phone call notifications but the in-app answer/decline options are not showing.

## ✅ What Has Been Fixed

### 1. Enhanced Incoming Call Display
- **Changed from Dialog to Full-Screen**: Converted `IncomingCallDialog` to `IncomingCallScreen` for better visibility
- **Full-Screen Interface**: Now uses a full-screen overlay instead of a small dialog
- **Better State Management**: Added duplicate call prevention with `_currentlyShowingCallId`
- **Proper Navigation**: Uses PageRouteBuilder for smooth transitions

### 2. Improved UI Elements
- **Larger Call Interface**: Full-screen layout with bigger buttons and text
- **Better Visual Hierarchy**: Clearer caller information and action buttons
- **Enhanced Button Sizes**: 80x80 pixel answer/decline buttons for easy tapping
- **Professional Layout**: Spacer widgets for proper alignment

### 3. State Management Fixes
- **Duplicate Prevention**: Prevents multiple dialogs for the same call
- **Proper Cleanup**: Calls `onDialogClosed` callback when screen closes
- **Mounted Check**: Ensures widgets are still mounted before navigation

## 🔍 Debugging Steps

### Step 1: Check if IncomingCallListener is Active
Add debug logging to verify the listener is working:

```dart
// In _IncomingCallListenerState.build()
print('🔍 [DEBUG] Current user: ${currentUser?.uid}');
print('🔍 [DEBUG] Listening for calls for user: ${currentUser?.uid}');

// In StreamBuilder
print('🔍 [DEBUG] Stream snapshot: ${snapshot.hasData}');
if (snapshot.hasData) {
  print('🔍 [DEBUG] Call data: ${snapshot.data?.toMap()}');
}
```

### Step 2: Verify Call Data Structure
Check if the call data is properly formatted:

```dart
// In _showIncomingCallDialog
print('🔍 [DEBUG] Showing call dialog for: ${callData.callId}');
print('🔍 [DEBUG] Caller: ${callData.callerName}');
print('🔍 [DEBUG] Type: ${callData.type}');
print('🔍 [DEBUG] Status: ${callData.status}');
```

### Step 3: Test Navigation
Verify the full-screen navigation is working:

```dart
// After Navigator.push
print('🔍 [DEBUG] Incoming call screen should be visible now');
```

### Step 4: Check User Provider
Ensure current user is available:

```dart
// In build method
final currentUser = Provider.of<UserProvider>(context).currentUser;
print('🔍 [DEBUG] UserProvider current user: ${currentUser?.uid}');
print('🔍 [DEBUG] UserProvider loading: ${Provider.of<UserProvider>(context).isLoading}');
```

## 🔧 Manual Testing Steps

### Test Scenario 1: App in Foreground
1. **Setup**: Have app open on receiver's device
2. **Action**: Make call from another device
3. **Expected**: Full-screen incoming call interface appears immediately
4. **Check**: Large answer/decline buttons are visible and responsive

### Test Scenario 2: App in Background
1. **Setup**: Put app in background on receiver's device
2. **Action**: Make call from another device
3. **Expected**: Notification appears + app brings full-screen interface when tapped

### Test Scenario 3: Multiple Calls
1. **Setup**: Make first call
2. **Action**: Make second call before first is answered
3. **Expected**: Only one call interface shown (prevents duplicates)

## 🐛 Common Issues & Solutions

### Issue 1: Screen Not Appearing
**Symptoms**: Notification works but no in-app interface
**Solution**: Check if IncomingCallListener is properly wrapping the app in main.dart

### Issue 2: Duplicate Screens
**Symptoms**: Multiple call screens stack up
**Solution**: `_currentlyShowingCallId` prevents this

### Issue 3: Navigation Issues
**Symptoms**: Screen doesn't close properly
**Solution**: `onDialogClosed` callback clears state

### Issue 4: User Provider Issues
**Symptoms**: No calls detected for user
**Solution**: Ensure UserProvider has current user set

## 🎯 Expected Behavior

### When Call Comes In:
1. ✅ **Notification appears** with sound/vibration
2. ✅ **Full-screen call interface** appears in app
3. ✅ **Large answer button** (green, 80x80px)
4. ✅ **Large decline button** (red, 80x80px)
5. ✅ **Caller information** clearly displayed
6. ✅ **Proper navigation** when buttons pressed

### UI Elements:
- **Background**: Semi-transparent black overlay
- **Caller Avatar**: Large circular image (160px diameter)
- **Caller Name**: Large white text (32px)
- **Call Type**: "Incoming audio/video call" subtitle
- **Buttons**: Positioned at bottom with good spacing

## 📱 Testing Commands

### Quick Test Call:
```dart
// Add this to any screen for testing
ElevatedButton(
  onPressed: () async {
    final callService = CallService();
    await callService.initiateCall(
      callerId: 'test_caller_id',
      callerName: 'Test Caller',
      receiverId: 'current_user_id', // Replace with actual user ID
      type: CallType.audio,
    );
  },
  child: Text('Test Incoming Call'),
)
```

## ✅ Verification Checklist

Before marking as complete:
- [ ] Full-screen call interface appears
- [ ] Answer/decline buttons are large and visible
- [ ] Caller information displays correctly
- [ ] Navigation works properly
- [ ] No duplicate call screens
- [ ] Proper cleanup when call ends
- [ ] Works with both audio and video calls

The implementation should now provide a clear, full-screen incoming call interface that's impossible to miss!