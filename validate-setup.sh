#!/bin/bash

# Validation script for Agora Firebase Functions setup
# This script checks if everything is ready for deployment

echo "🔍 Validating Agora Firebase Functions Setup"
echo "============================================="
echo ""

# Check if we're in the right directory
if [ ! -f firebase.json ]; then
    echo "❌ Not in Firebase project directory"
    echo "📝 Please run this script from your Firebase project root"
    exit 1
fi

echo "✅ Firebase project detected"

# Check if functions directory exists
if [ ! -d functions ]; then
    echo "❌ Functions directory not found"
    echo "📝 Please run 'firebase init functions' first"
    exit 1
fi

echo "✅ Functions directory found"

# Check if package.json has agora-token
if grep -q "agora-token" functions/package.json; then
    echo "✅ agora-token dependency found"
else
    echo "❌ agora-token dependency missing"
    exit 1
fi

# Check if index.js has generateAgoraToken function
if grep -q "generateAgoraToken" functions/index.js; then
    echo "✅ generateAgoraToken function found"
else
    echo "❌ generateAgoraToken function missing"
    exit 1
fi

# Check if .env.example exists
if [ -f functions/.env.example ]; then
    echo "✅ Environment template found"
else
    echo "❌ .env.example not found"
fi

# Check if dependencies are installed
if [ -d functions/node_modules ]; then
    echo "✅ Dependencies installed"
else
    echo "⚠️  Dependencies not installed"
    echo "📝 Run 'cd functions && npm install'"
fi

# Check Flutter config
if grep -q "generateAgoraToken" lib/config/agora_config.dart; then
    echo "✅ Flutter app configured for Firebase Functions"
else
    echo "⚠️  Flutter app not configured for Firebase Functions"
fi

# Check ESLint
echo ""
echo "🔍 Running ESLint check..."
cd functions
if npm run lint > /dev/null 2>&1; then
    echo "✅ ESLint passed"
else
    echo "❌ ESLint failed"
    echo "📝 Fix ESLint errors before deployment"
    exit 1
fi

cd ..

echo ""
echo "🎉 Validation Complete!"
echo "======================="
echo ""
echo "✅ Your setup is ready for deployment"
echo ""
echo "📋 Next Steps:"
echo "1. Get your Agora credentials from https://console.agora.io/"
echo "2. Run ./setup-agora-firebase.sh (automated)"
echo "   OR manually configure and deploy (see DEPLOY_INSTRUCTIONS.md)"
echo ""
echo "🚀 Ready to deploy Firebase Functions!"