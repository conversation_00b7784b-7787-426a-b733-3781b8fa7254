#!/bin/bash

# Complete setup script for Agora.io with Firebase Functions
# This script guides you through the entire setup process

echo "🚀 Agora.io + Firebase Functions Setup"
echo "========================================"
echo ""

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI not found!"
    echo "📦 Please install Firebase CLI first:"
    echo "   npm install -g firebase-tools"
    echo "   firebase login"
    exit 1
fi

# Check if we're in a Firebase project
if [ ! -f firebase.json ]; then
    echo "❌ Not in a Firebase project directory!"
    echo "📝 Please run 'firebase init' first"
    exit 1
fi

echo "✅ Firebase CLI detected"

# Check if functions directory exists
if [ ! -d functions ]; then
    echo "❌ Functions directory not found!"
    echo "📝 Please initialize Firebase Functions first:"
    echo "   firebase init functions"
    exit 1
fi

echo "✅ Functions directory found"

# Step 1: Agora Console Setup
echo ""
echo "📋 Step 1: Agora Console Setup"
echo "=============================="
echo ""
echo "1. Go to https://console.agora.io/"
echo "2. Create a new project or select existing one"
echo "3. ⚠️  IMPORTANT: Choose 'Secured mode: APP ID + Token'"
echo "4. Copy your App ID and App Certificate"
echo ""
read -p "Press Enter when you have your Agora credentials ready..."

# Step 2: Environment Configuration
echo ""
echo "🔧 Step 2: Environment Configuration"
echo "===================================="
echo ""

cd functions

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env file from template"
fi

echo "Please enter your Agora credentials:"
echo ""

# Get App ID
read -p "Enter your Agora App ID: " AGORA_APP_ID
if [ -z "$AGORA_APP_ID" ]; then
    echo "❌ App ID cannot be empty!"
    exit 1
fi

# Get App Certificate
read -p "Enter your Agora App Certificate: " AGORA_APP_CERTIFICATE
if [ -z "$AGORA_APP_CERTIFICATE" ]; then
    echo "❌ App Certificate cannot be empty!"
    exit 1
fi

# Update .env file
cat > .env << EOF
# Agora.io Configuration
AGORA_APP_ID=$AGORA_APP_ID
AGORA_APP_CERTIFICATE=$AGORA_APP_CERTIFICATE
EOF

echo "✅ Environment variables saved to .env"

# Step 3: Install Dependencies
echo ""
echo "📦 Step 3: Installing Dependencies"
echo "=================================="
echo ""

npm install
if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Step 4: Set Firebase Config
echo ""
echo "🔧 Step 4: Setting Firebase Configuration"
echo "========================================="
echo ""

firebase functions:config:set \
    agora.app_id="$AGORA_APP_ID" \
    agora.app_certificate="$AGORA_APP_CERTIFICATE"

if [ $? -ne 0 ]; then
    echo "❌ Failed to set Firebase configuration"
    exit 1
fi

echo "✅ Firebase configuration set successfully"

# Step 5: Deploy Functions
echo ""
echo "🚀 Step 5: Deploying Functions"
echo "==============================="
echo ""

firebase deploy --only functions
if [ $? -ne 0 ]; then
    echo "❌ Failed to deploy functions"
    exit 1
fi

echo "✅ Functions deployed successfully"

# Step 6: Flutter App Configuration
echo ""
echo "📱 Step 6: Flutter App Configuration"
echo "===================================="
echo ""

cd ..

# Update Agora config
if [ -f lib/config/agora_config.dart ]; then
    # Backup original file
    cp lib/config/agora_config.dart lib/config/agora_config.dart.bak
    
    # Update App ID
    sed -i.tmp "s/YOUR_AGORA_APP_ID/$AGORA_APP_ID/g" lib/config/agora_config.dart
    rm lib/config/agora_config.dart.tmp
    
    echo "✅ Updated Flutter Agora configuration"
else
    echo "⚠️  Could not find lib/config/agora_config.dart"
    echo "📝 Please manually update your Agora App ID in the config file"
fi

# Step 7: Test Setup
echo ""
echo "🧪 Step 7: Testing Setup"
echo "========================"
echo ""

echo "To test your setup:"
echo "1. Firebase Functions Shell:"
echo "   cd functions"
echo "   firebase functions:shell"
echo "   generateAgoraToken({channelName: 'test', uid: 12345})"
echo ""
echo "2. Flutter App:"
echo "   flutter run"
echo "   Open any chat and tap call buttons"
echo ""

# Final Summary
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "✅ Agora.io credentials configured"
echo "✅ Firebase Functions deployed"
echo "✅ Flutter app updated"
echo ""
echo "📋 Configuration Summary:"
echo "  - Agora App ID: $AGORA_APP_ID"
echo "  - Token Server: Firebase Functions (generateAgoraToken)"
echo "  - Security: Token-based authentication enabled"
echo ""
echo "🔗 Useful Links:"
echo "  - Agora Console: https://console.agora.io/"
echo "  - Firebase Console: https://console.firebase.google.com/"
echo "  - Documentation: ./FIREBASE_AGORA_SETUP.md"
echo ""
echo "Happy calling! 📞📹"