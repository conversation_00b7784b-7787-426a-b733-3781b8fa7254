# Tolk - Flutter Chat Application

A modern, feature-rich chat application built with Flutter and Firebase, offering real-time messaging, media sharing, voice messages, and emoji support with professional-grade performance optimizations.

## 🚀 Features

### Core Messaging
- **Real-time Chat**: Instant messaging with Firebase Firestore
- **Media Sharing**: Images, videos, files, and documents
- **Voice Messages**: Record and send audio messages with playback controls
- **Emoji Support**: Rich emoji picker with categories and search
- **Read Receipts**: Blue checkmarks for read messages
- **Unread Count**: Real-time unread message badges with zero-delay updates
- **Message Status**: Sent, delivered, and read indicators

### User Experience
- **Zero-Delay UI**: Optimized streams prevent UI blinking/flickering
- **Background Processing**: Unread messages marked automatically without user intervention
- **Smooth Navigation**: Professional chat experience like WhatsApp/Telegram
- **Dark Theme**: Modern dark UI design with purple accent
- **Responsive Design**: Works seamlessly on all screen sizes

### Technical Features
- **Stream Optimization**: Prevents unnecessary UI rebuilds using distinct filtering
- **Data Equality**: Deep equality checks for models prevent false updates
- **Error Handling**: Robust error management with fallback mechanisms
- **Performance**: Efficient ListView with widget keys for optimal rendering
- **Memory Management**: Proper stream disposal and resource cleanup

## 📁 Project Structure

```
lib/
├── main.dart                           # App entry point with Firebase initialization
├── models/                             # Data models with equality implementations
│   ├── chat_models.dart               # ChatRoom, Message, MessageType, MessageStatus
│   └── user_model.dart                # UserModel with comprehensive equality checks
├── screens/                            # UI screens organized by feature
│   ├── auth/                          # Authentication flow
│   │   ├── login_screen.dart          # User login with validation
│   │   └── signup_screen.dart         # User registration with validation
│   ├── chat/                          # Chat functionality
│   │   ├── chat_list_screen.dart      # Home screen with optimized chat list
│   │   ├── chat_screen.dart           # Individual chat with media support
│   │   └── user_search_screen.dart    # Find users to start conversations
│   └── profile/                       # User management
│       └── profile_screen.dart        # User profile editing and settings
├── services/                           # Business logic layer
│   ├── auth_service.dart              # Authentication operations
│   └── chat_service.dart              # Chat operations with optimization
├── providers/                          # State management
│   └── user_provider.dart             # Global user state with Provider pattern
├── widgets/                            # Reusable UI components
│   ├── chat/                          # Chat-specific widgets
│   │   ├── emoji_picker_widget.dart   # Emoji selection with categories
│   │   ├── media_message_widget.dart  # Media display and interaction
│   │   └── voice_message_widget.dart  # Voice message playback controls
│   └── common/                        # Common UI components
│       └── custom_text_field.dart     # Styled text input with validation
├── utils/                              # Utility classes and constants
│   ├── app_colors.dart                # Color scheme and theme constants
│   ├── app_strings.dart               # Localized string constants
│   └── navigation_helper.dart         # Navigation utilities and helpers
└── firebase_options.dart              # Firebase configuration (auto-generated)
```

## 🏗️ Architecture Overview

### State Management Strategy
- **Provider Pattern**: Global user state management
- **Stream-based**: Real-time data synchronization with Firestore
- **Optimized Streams**: Custom distinct filtering prevents unnecessary rebuilds
- **Local State**: UI-only state managed with StatefulWidget

### Data Layer Architecture
- **Firebase Firestore**: Real-time NoSQL database for messages and chat rooms
- **Firebase Storage**: Secure file storage for media content
- **Firebase Auth**: User authentication and session management
- **Offline Support**: Firestore offline persistence enabled

### UI Layer Design
- **Material Design 3**: Modern Flutter UI components
- **Custom Widgets**: Reusable chat-specific components
- **Responsive Layout**: Adaptive design for different screen sizes
- **Dark Theme**: Consistent dark mode throughout the app

## 🔧 Key Technical Implementations

### Stream Optimization (Prevents UI Blinking)
```dart
// Custom distinct filtering prevents unnecessary UI rebuilds
.distinct((previous, current) {
  // Only emit if the lists are actually different
  if (previous.length != current.length) return false;
  for (int i = 0; i < previous.length; i++) {
    if (previous[i] != current[i]) return false;
  }
  return true;
});
```

### Zero-Delay Unread Message Marking
```dart
// Immediate UI update strategy
try {
  // IMMEDIATE: Reset unread count to 0 first (zero delay for UI)
  await _chatRoomsCollection.doc(chatRoomId).update({
    'unreadCount.$currentUserId': 0,
  });

  // BACKGROUND: Mark individual messages as read
  // (happens after UI already shows 0 unread)
} catch (e) {
  // Silent fallback - don't break the UI
}
```

### Data Model Equality Implementation
```dart
@override
bool operator ==(Object other) {
  if (identical(this, other)) return true;
  if (other is! ChatRoom) return false;

  return id == other.id &&
      _listEquals(participants, other.participants) &&
      isGroupChat == other.isGroupChat &&
      _mapEquals(unreadCount, other.unreadCount) &&
      lastMessage == other.lastMessage &&
      lastMessageTime == other.lastMessageTime &&
      createdAt == other.createdAt &&
      createdBy == other.createdBy;
}

@override
int get hashCode => Object.hash(
  id, participants, isGroupChat, unreadCount,
  lastMessage, lastMessageTime, createdAt, createdBy,
);
```

## 📱 Core Features Documentation

### Chat Service (`lib/services/chat_service.dart`)
**Primary Functions:**
- `createOrGetChatRoom(String otherUserId)`: Creates new chat or retrieves existing one
- `sendMessage()`: Sends text/media messages with metadata
- `markMessagesAsRead(String chatRoomId)`: Zero-delay read marking with background processing
- `getChatRooms()`: Optimized chat list stream with distinct filtering
- `getMessages(String chatRoomId)`: Optimized message stream with distinct filtering
- `sendImageMessage()`: Image upload to Firebase Storage and message creation
- `sendVoiceMessage()`: Voice recording upload and message creation
- `sendFileMessage()`: File upload with automatic type detection

**Key Optimizations:**
- Stream deduplication prevents unnecessary UI updates
- Immediate unread count reset for zero-delay user experience
- Robust error handling with silent fallbacks
- Efficient batch operations for multiple updates

### Chat Models (`lib/models/chat_models.dart`)
**Data Structures:**

#### ChatRoom
```dart
class ChatRoom {
  final String id;
  final List<String> participants;
  final bool isGroupChat;
  final Map<String, int> unreadCount;  // userId -> count
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final DateTime? createdAt;
  final String? createdBy;
}
```

#### Message
```dart
class Message {
  final String id;
  final String chatRoomId;
  final String senderId;
  final String? text;
  final String? mediaUrl;
  final MessageType type;
  final MessageStatus status;
  final DateTime timestamp;
  final List<String> readBy;
  final Map<String, dynamic>? metadata;
}
```

#### Enums
- `MessageType`: text, image, video, audio, file, voice
- `MessageStatus`: sent, delivered, read

### UI Components

#### Chat List Screen (`lib/screens/chat/chat_list_screen.dart`)
**Features:**
- Real-time chat list with unread count badges
- Optimized ListView with ValueKey for performance
- Automatic read marking on chat tap (zero delay)
- Pull-to-refresh functionality
- Empty state handling

**Key Implementation:**
```dart
// Zero-delay read marking on tap
onTap: () async {
  await _chatService.markMessagesAsRead(chatRoom.id);
  if (mounted && currentContext.mounted) {
    pushScreen(currentContext, ChatScreen(...));
  }
}
```

#### Chat Screen (`lib/screens/chat/chat_screen.dart`)
**Features:**
- Real-time message display with reverse chronological order
- Media message support (images, videos, files, voice)
- Voice recording with visual feedback
- Emoji picker integration
- Automatic read marking on screen load
- App lifecycle handling for background/foreground

**Key Implementation:**
```dart
// Automatic read marking with lifecycle management
@override
void initState() {
  super.initState();
  WidgetsBinding.instance.addObserver(this);
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _markMessagesAsRead();
  });
}

@override
void didChangeAppLifecycleState(AppLifecycleState state) {
  if (state == AppLifecycleState.resumed) {
    _markMessagesAsRead();
  }
}
```

#### Media Widgets
**MediaMessageWidget** (`lib/widgets/chat/media_message_widget.dart`):
- Image display with cached network images
- Video playback controls
- File download and open functionality
- Loading states and error handling

**VoiceMessageWidget** (`lib/widgets/chat/voice_message_widget.dart`):
- Audio playback with progress indicator
- Play/pause controls
- Duration display
- Waveform visualization (if implemented)

**EmojiPickerWidget** (`lib/widgets/chat/emoji_picker_widget.dart`):
- Categorized emoji selection
- Search functionality
- Recent emojis tracking
- Smooth animations

## 🔥 Firebase Integration

### Firestore Database Structure
```
users/
├── {userId}/
│   ├── uid: string                    # User unique identifier
│   ├── email: string                  # User email address
│   ├── displayName: string            # User display name
│   ├── profilePicture: string?        # Profile image URL (optional)
│   ├── isOnline: boolean              # Online status
│   ├── lastSeen: timestamp?           # Last seen timestamp (when offline)
│   ├── createdAt: timestamp           # Account creation time
│   └── updatedAt: timestamp           # Last profile update

chatRooms/
├── {chatRoomId}/
│   ├── participants: string[]         # Array of user IDs in the chat
│   ├── isGroupChat: boolean           # Whether this is a group chat
│   ├── unreadCount: {userId: number}  # Unread count per user
│   ├── lastMessage: string            # Preview of last message
│   ├── lastMessageTime: timestamp     # Time of last message
│   ├── createdAt: timestamp           # Chat creation time
│   └── createdBy: string              # User ID who created the chat

messages/
├── {messageId}/
│   ├── chatRoomId: string             # Reference to chat room
│   ├── senderId: string               # User ID of sender
│   ├── text: string                   # Message text content
│   ├── mediaUrl: string?              # URL for media content (optional)
│   ├── type: number                   # MessageType enum (0=text, 1=image, etc.)
│   ├── status: number                 # MessageStatus enum (0=sent, 1=delivered, 2=read)
│   ├── timestamp: timestamp           # Message creation time
│   ├── readBy: string[]               # Array of user IDs who read the message
│   └── metadata: object?              # Additional data (file size, duration, etc.)
```

### Firebase Storage Structure
```
chat_images/
├── {userId}/
│   └── {timestamp}_{filename}         # User's uploaded images

chat_files/
├── {userId}/
│   └── {timestamp}_{filename}         # User's uploaded files

chat_audio/
├── {userId}/
│   └── {timestamp}_voice.m4a          # User's voice messages
```

### Security Rules
**Firestore Rules:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Chat rooms accessible to participants
    match /chatRooms/{chatRoomId} {
      allow read, write: if request.auth != null &&
        request.auth.uid in resource.data.participants;
    }

    // Messages accessible to chat participants
    match /messages/{messageId} {
      allow read, write: if request.auth != null &&
        exists(/databases/$(database)/documents/chatRooms/$(resource.data.chatRoomId)) &&
        request.auth.uid in get(/databases/$(database)/documents/chatRooms/$(resource.data.chatRoomId)).data.participants;
    }
  }
}
```

**Storage Rules:**
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /chat_images/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /chat_files/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    match /chat_audio/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## 🎨 UI/UX Design System

### Color Scheme (`lib/utils/app_colors.dart`)
```dart
class AppColors {
  static const Color appColor = Color(0xFF6C5CE7);        // Primary purple
  static const Color splashColor = Color(0xFF1A1A1A);     // Dark background
  static const Color cardColor = Color(0xFF2D2D2D);       // Card background
  static const Color textPrimary = Color(0xFFFFFFFF);     // Primary text
  static const Color textSecondary = Color(0xFFB0B0B0);   // Secondary text
  static const Color success = Color(0xFF00D4AA);         // Success green
  static const Color error = Color(0xFFFF6B6B);           // Error red
  static const Color warning = Color(0xFFFFD93D);         // Warning yellow
}
```

### Typography & Spacing
- **Font Family**: System default (Roboto on Android, SF Pro on iOS)
- **Text Scales**: 12sp (caption), 14sp (body), 16sp (subtitle), 18sp (title), 24sp (headline)
- **Spacing**: 4dp, 8dp, 12dp, 16dp, 24dp, 32dp grid system
- **Border Radius**: 8dp (small), 12dp (medium), 16dp (large), 24dp (extra large)

### Animation & Transitions
- **Page Transitions**: Slide transitions with 300ms duration
- **Message Animations**: Fade in with scale (200ms)
- **Loading States**: Circular progress with app color
- **Emoji Picker**: Slide up animation (250ms)
- **Voice Recording**: Pulse animation for recording indicator

### Responsive Design
- **Breakpoints**: Mobile (<600dp), Tablet (600-1200dp), Desktop (>1200dp)
- **Chat Bubbles**: Max width 80% of screen width
- **Media**: Responsive sizing with aspect ratio preservation
- **Keyboard**: Proper handling with resize and scroll behavior

## 🔧 Performance Optimizations

### Stream Efficiency
- **Distinct Filtering**: Custom `.distinct()` implementation prevents duplicate emissions
- **Widget Keys**: `ValueKey` for ListView items enables efficient updates
- **Smart Loading**: Loading states only shown on initial data fetch
- **Memory Management**: Proper stream disposal and resource cleanup

### UI Optimizations
- **Zero-Delay Updates**: Immediate UI feedback before background processing
- **Background Processing**: Non-blocking operations for better responsiveness
- **Efficient Rebuilds**: Minimal widget tree changes using equality checks
- **Cached Images**: Network image caching with `cached_network_image`

### Database Optimizations
- **Batch Operations**: Multiple Firestore updates in single transaction
- **Indexed Queries**: Proper indexing for chat room and message queries
- **Pagination**: Efficient message loading (ready for implementation)
- **Offline Support**: Firestore offline persistence enabled

## 🚀 Getting Started

### Prerequisites
- **Flutter SDK**: Version 3.0 or higher
- **Dart SDK**: Version 2.17 or higher
- **Firebase Project**: With Firestore, Storage, and Authentication enabled
- **Development Environment**: Android Studio, VS Code, or similar
- **Platform SDKs**: Android SDK (API 21+) and/or Xcode (iOS 11+)

### Installation Steps
1. **Clone Repository**:
   ```bash
   git clone <repository-url>
   cd tolk
   ```

2. **Install Dependencies**:
   ```bash
   flutter pub get
   ```

3. **Firebase Setup**:
   - Create Firebase project at [console.firebase.google.com](https://console.firebase.google.com)
   - Enable Authentication (Email/Password)
   - Enable Firestore Database
   - Enable Firebase Storage
   - Add Android/iOS apps to project
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place configuration files in appropriate directories

4. **Configure Firebase**:
   ```bash
   flutter packages pub run build_runner build
   ```

5. **Run Application**:
   ```bash
   flutter run
   ```

### Firebase Configuration Details
**Required Firebase Services:**
- **Authentication**: Email/Password provider enabled
- **Firestore**: Database with security rules configured
- **Storage**: File storage with security rules configured

**Security Rules Setup:**
1. Copy Firestore rules from documentation above
2. Copy Storage rules from documentation above
3. Deploy rules through Firebase Console

## 📝 Development Guidelines

### Code Style & Standards
- **Dart Style**: Follow official Dart style guide
- **Naming Conventions**:
  - Classes: `PascalCase`
  - Variables/Functions: `camelCase`
  - Constants: `SCREAMING_SNAKE_CASE`
  - Files: `snake_case.dart`
- **Documentation**: Add dartdoc comments for public APIs
- **Error Handling**: Always implement try-catch for async operations

### State Management Best Practices
- **Provider**: Use for global state (user authentication)
- **Streams**: Use for real-time data (messages, chat rooms)
- **Local State**: Use StatefulWidget for UI-only state
- **Immutability**: Prefer immutable data structures

### Performance Guidelines
- **Widget Keys**: Always use keys for ListView items
- **Equality Checks**: Implement `==` and `hashCode` for data models
- **Stream Optimization**: Use `.distinct()` to prevent unnecessary rebuilds
- **Memory Management**: Dispose controllers and close streams properly

### Testing Strategy
- **Unit Tests**: Test business logic in services
- **Widget Tests**: Test UI components in isolation
- **Integration Tests**: Test complete user flows
- **Firebase Emulator**: Use for local testing

## 🐛 Troubleshooting

### Common Issues & Solutions

#### 1. Unread Count Not Updating
**Symptoms**: Unread badges persist after viewing messages
**Solution**:
- Check `markMessagesAsRead()` implementation
- Verify Firestore security rules allow updates
- Ensure user authentication is working

#### 2. UI Blinking/Flickering
**Symptoms**: Chat list or messages flicker during updates
**Solution**:
- Verify stream optimization with `.distinct()`
- Check data model equality implementations
- Ensure widget keys are properly set

#### 3. Media Upload Failures
**Symptoms**: Images/files fail to upload
**Solution**:
- Check Firebase Storage security rules
- Verify internet connectivity
- Check file size limits and formats

#### 4. Messages Not Sending
**Symptoms**: Messages appear stuck in sending state
**Solution**:
- Verify Firestore security rules
- Check user authentication status
- Ensure chat room exists and user is participant

#### 5. Voice Messages Not Playing
**Symptoms**: Audio playback fails or no controls appear
**Solution**:
- Check audio file format compatibility
- Verify Firebase Storage URLs are accessible
- Ensure proper audio permissions

### Debug Features
- **Comprehensive Logging**: ChatService includes detailed debug output
- **Error Handling**: Silent fallbacks prevent UI crashes
- **Development Mode**: Additional logging in debug builds

### Performance Monitoring
- **Stream Monitoring**: Track stream emissions and rebuilds
- **Memory Usage**: Monitor for memory leaks in streams
- **Network Usage**: Track Firebase operations and data transfer

## 🔮 Future Enhancements

### Planned Features
- **Group Chat Support**: Multi-participant conversations
- **Message Reactions**: Emoji reactions to messages
- **Push Notifications**: Real-time notifications when app is closed
- **Message Search**: Search through chat history
- **Chat Backup/Restore**: Cloud backup of chat data
- **Video Calling**: Integrated video call functionality
- **Message Encryption**: End-to-end encryption for privacy
- **Message Scheduling**: Send messages at specific times
- **Chat Themes**: Customizable chat bubble themes
- **Message Translation**: Auto-translate messages

### Technical Improvements
- **Offline Support**: Enhanced offline message queuing
- **Message Pagination**: Efficient loading of chat history
- **Advanced Caching**: Intelligent data caching strategies
- **Performance Monitoring**: Real-time performance analytics
- **Automated Testing**: Comprehensive test coverage
- **CI/CD Pipeline**: Automated build and deployment
- **Code Generation**: Generate models from schema
- **Internationalization**: Multi-language support

### Architecture Evolution
- **Clean Architecture**: Implement clean architecture patterns
- **Dependency Injection**: Use GetIt or similar for DI
- **State Management**: Consider Bloc or Riverpod for complex state
- **Microservices**: Split into smaller, focused services
- **GraphQL**: Consider GraphQL for more efficient data fetching

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

### How to Contribute
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Contribution Guidelines
- Follow the existing code style and conventions
- Add tests for new features
- Update documentation for API changes
- Ensure all tests pass before submitting PR
- Write clear, descriptive commit messages

### Code Review Process
- All changes require code review
- Automated tests must pass
- Performance impact should be considered
- Security implications must be evaluated

---

## 📚 Additional Resources

### Documentation Links
- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Dart Language Guide](https://dart.dev/guides)
- [Material Design Guidelines](https://material.io/design)

### Community & Support
- [Flutter Community](https://flutter.dev/community)
- [Firebase Community](https://firebase.google.com/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)

---


